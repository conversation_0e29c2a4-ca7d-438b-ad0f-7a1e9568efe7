"""
Configuration management for Silexa
"""

import os
from dataclasses import dataclass, field
from typing import Dict, List, Tuple, Optional
from pathlib import Path


@dataclass
class VideoConfig:
    """Video processing configuration"""
    input_width: int = 640
    input_height: int = 480
    fps: int = 30
    buffer_size: int = 10
    face_detection_confidence: float = 0.5
    lip_region_padding: int = 20


@dataclass
class ModelConfig:
    """Model configuration"""
    model_type: str = "lstm"  # lstm, transformer, cnn_lstm
    sequence_length: int = 16
    feature_dim: int = 68  # MediaPipe lip landmarks
    hidden_units: int = 256
    num_layers: int = 3
    dropout_rate: float = 0.2
    learning_rate: float = 0.001
    batch_size: int = 32
    epochs: int = 100
    early_stopping_patience: int = 10


@dataclass
class TTSConfig:
    """Text-to-Speech configuration"""
    language: str = "en"
    slow_speech: bool = False
    voice_speed: float = 1.0
    emotion_detection: bool = True
    supported_languages: List[str] = field(default_factory=lambda: [
        "en", "es", "fr", "de", "it", "pt", "ru", "ja", "ko", "zh"
    ])


@dataclass
class DataConfig:
    """Data processing configuration"""
    data_dir: str = "data"
    raw_data_dir: str = "data/raw"
    processed_data_dir: str = "data/processed"
    models_dir: str = "data/models"
    train_split: float = 0.7
    val_split: float = 0.15
    test_split: float = 0.15
    augmentation_enabled: bool = True
    max_sequence_length: int = 50


@dataclass
class UIConfig:
    """User interface configuration"""
    window_width: int = 1200
    window_height: int = 800
    theme: str = "dark"
    font_size: int = 12
    show_confidence: bool = True
    real_time_display: bool = True


@dataclass
class SilexaConfig:
    """Main Silexa configuration"""
    video: VideoConfig = field(default_factory=VideoConfig)
    model: ModelConfig = field(default_factory=ModelConfig)
    tts: TTSConfig = field(default_factory=TTSConfig)
    data: DataConfig = field(default_factory=DataConfig)
    ui: UIConfig = field(default_factory=UIConfig)
    
    # System settings
    device: str = "auto"  # auto, cpu, gpu
    num_workers: int = 4
    log_level: str = "INFO"
    save_predictions: bool = True
    
    def __post_init__(self):
        """Post-initialization setup"""
        # Create directories if they don't exist
        for dir_path in [self.data.data_dir, self.data.raw_data_dir, 
                        self.data.processed_data_dir, self.data.models_dir]:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def from_yaml(cls, config_path: str) -> 'SilexaConfig':
        """Load configuration from YAML file"""
        import yaml
        
        with open(config_path, 'r') as f:
            config_dict = yaml.safe_load(f)
        
        return cls(**config_dict)
    
    def to_yaml(self, config_path: str) -> None:
        """Save configuration to YAML file"""
        import yaml
        from dataclasses import asdict
        
        with open(config_path, 'w') as f:
            yaml.dump(asdict(self), f, default_flow_style=False)
    
    def get_device(self) -> str:
        """Get the appropriate device for computation"""
        if self.device == "auto":
            try:
                import tensorflow as tf
                if tf.config.list_physical_devices('GPU'):
                    return "gpu"
                else:
                    return "cpu"
            except ImportError:
                return "cpu"
        return self.device


# Default configuration instance
default_config = SilexaConfig()
