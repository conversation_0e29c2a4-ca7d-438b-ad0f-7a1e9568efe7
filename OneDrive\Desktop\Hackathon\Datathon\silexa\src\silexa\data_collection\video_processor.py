"""
Video processing module for capturing and preprocessing video frames
"""

import cv2
import numpy as np
from typing import Optional, List, Tuple, Generator
import threading
import queue
import time
from loguru import logger

from ..core.config import VideoConfig


class VideoProcessor:
    """
    Handles video capture, preprocessing, and frame management.
    
    Features:
    - Real-time video capture from camera
    - Video file processing
    - Frame preprocessing and normalization
    - Face detection and tracking
    - Thread-safe frame buffering
    """
    
    def __init__(self, config: VideoConfig):
        """
        Initialize the video processor.
        
        Args:
            config: Video configuration object
        """
        self.config = config
        self.cap = None
        self.is_capturing = False
        
        # Face detection
        self.face_cascade = cv2.CascadeClassifier(
            cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
        )
        
        # Frame buffer for real-time processing
        self.frame_buffer = queue.Queue(maxsize=config.buffer_size)
        self.capture_thread = None
        
        # Statistics
        self.frames_processed = 0
        self.faces_detected = 0
        
        logger.info("VideoProcessor initialized")
    
    def start_capture(self, camera_id: int = 0) -> bool:
        """
        Start video capture from camera.
        
        Args:
            camera_id: Camera device ID
            
        Returns:
            True if capture started successfully, False otherwise
        """
        try:
            self.cap = cv2.VideoCapture(camera_id)
            
            if not self.cap.isOpened():
                logger.error(f"Failed to open camera {camera_id}")
                return False
            
            # Set camera properties
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.config.input_width)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.config.input_height)
            self.cap.set(cv2.CAP_PROP_FPS, self.config.fps)
            
            self.is_capturing = True
            
            # Start capture thread
            self.capture_thread = threading.Thread(
                target=self._capture_loop,
                daemon=True
            )
            self.capture_thread.start()
            
            logger.info(f"Video capture started on camera {camera_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error starting video capture: {e}")
            return False
    
    def stop_capture(self) -> None:
        """Stop video capture."""
        self.is_capturing = False
        
        if self.capture_thread and self.capture_thread.is_alive():
            self.capture_thread.join(timeout=2.0)
        
        if self.cap:
            self.cap.release()
            self.cap = None
        
        # Clear frame buffer
        while not self.frame_buffer.empty():
            try:
                self.frame_buffer.get_nowait()
            except queue.Empty:
                break
        
        logger.info("Video capture stopped")
    
    def get_frame(self) -> Optional[np.ndarray]:
        """
        Get the latest frame from the buffer.
        
        Returns:
            Preprocessed frame or None if no frame available
        """
        try:
            frame = self.frame_buffer.get_nowait()
            return self.preprocess_frame(frame)
        except queue.Empty:
            return None
    
    def process_video_file(self, video_path: str) -> Generator[np.ndarray, None, None]:
        """
        Process frames from a video file.
        
        Args:
            video_path: Path to the video file
            
        Yields:
            Preprocessed frames
        """
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            logger.error(f"Failed to open video file: {video_path}")
            return
        
        try:
            frame_count = 0
            while True:
                ret, frame = cap.read()
                
                if not ret:
                    break
                
                processed_frame = self.preprocess_frame(frame)
                if processed_frame is not None:
                    yield processed_frame
                    frame_count += 1
            
            logger.info(f"Processed {frame_count} frames from {video_path}")
            
        finally:
            cap.release()
    
    def preprocess_frame(self, frame: np.ndarray) -> Optional[np.ndarray]:
        """
        Preprocess a single frame.
        
        Args:
            frame: Raw frame from video capture
            
        Returns:
            Preprocessed frame or None if processing failed
        """
        if frame is None:
            return None
        
        try:
            # Resize frame
            frame = cv2.resize(frame, (self.config.input_width, self.config.input_height))
            
            # Convert to RGB (MediaPipe expects RGB)
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # Normalize pixel values
            frame_normalized = frame_rgb.astype(np.float32) / 255.0
            
            self.frames_processed += 1
            return frame_normalized
            
        except Exception as e:
            logger.error(f"Error preprocessing frame: {e}")
            return None
    
    def detect_face(self, frame: np.ndarray) -> Optional[Tuple[int, int, int, int]]:
        """
        Detect face in frame using Haar cascades.
        
        Args:
            frame: Input frame (RGB format)
            
        Returns:
            Face bounding box (x, y, w, h) or None if no face detected
        """
        try:
            # Convert to grayscale for face detection
            gray = cv2.cvtColor(frame, cv2.COLOR_RGB2GRAY)
            
            # Detect faces
            faces = self.face_cascade.detectMultiScale(
                gray,
                scaleFactor=1.1,
                minNeighbors=5,
                minSize=(30, 30),
                flags=cv2.CASCADE_SCALE_IMAGE
            )
            
            if len(faces) > 0:
                # Return the largest face
                largest_face = max(faces, key=lambda f: f[2] * f[3])
                self.faces_detected += 1
                return tuple(largest_face)
            
            return None
            
        except Exception as e:
            logger.error(f"Error in face detection: {e}")
            return None
    
    def extract_face_region(self, frame: np.ndarray, face_box: Tuple[int, int, int, int]) -> Optional[np.ndarray]:
        """
        Extract face region from frame.
        
        Args:
            frame: Input frame
            face_box: Face bounding box (x, y, w, h)
            
        Returns:
            Cropped face region or None if extraction failed
        """
        try:
            x, y, w, h = face_box
            
            # Add padding
            padding = self.config.lip_region_padding
            x_start = max(0, x - padding)
            y_start = max(0, y - padding)
            x_end = min(frame.shape[1], x + w + padding)
            y_end = min(frame.shape[0], y + h + padding)
            
            face_region = frame[y_start:y_end, x_start:x_end]
            
            return face_region
            
        except Exception as e:
            logger.error(f"Error extracting face region: {e}")
            return None
    
    def _capture_loop(self) -> None:
        """Main capture loop running in separate thread."""
        while self.is_capturing and self.cap:
            try:
                ret, frame = self.cap.read()
                
                if not ret:
                    logger.warning("Failed to read frame from camera")
                    time.sleep(0.01)
                    continue
                
                # Add frame to buffer (non-blocking)
                try:
                    self.frame_buffer.put_nowait(frame)
                except queue.Full:
                    # Remove oldest frame and add new one
                    try:
                        self.frame_buffer.get_nowait()
                        self.frame_buffer.put_nowait(frame)
                    except queue.Empty:
                        pass
                
            except Exception as e:
                logger.error(f"Error in capture loop: {e}")
                time.sleep(0.1)
    
    def get_statistics(self) -> dict:
        """
        Get processing statistics.
        
        Returns:
            Dictionary with processing statistics
        """
        return {
            'frames_processed': self.frames_processed,
            'faces_detected': self.faces_detected,
            'is_capturing': self.is_capturing,
            'buffer_size': self.frame_buffer.qsize(),
            'detection_rate': self.faces_detected / max(1, self.frames_processed)
        }
    
    def reset_statistics(self) -> None:
        """Reset processing statistics."""
        self.frames_processed = 0
        self.faces_detected = 0
    
    def __del__(self):
        """Cleanup on destruction."""
        self.stop_capture()
