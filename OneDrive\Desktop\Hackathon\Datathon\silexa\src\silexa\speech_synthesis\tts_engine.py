"""
Text-to-Speech engine for converting predicted text to natural speech
"""

import os
import tempfile
import threading
import queue
from typing import Optional, Dict, Any, List
import time
from loguru import logger

try:
    from gtts import gTTS
    import pygame
    from pydub import AudioSegment
    from pydub.playback import play
    AUDIO_AVAILABLE = True
except ImportError:
    AUDIO_AVAILABLE = False
    logger.warning("Audio libraries not available. TTS functionality will be limited.")

from ..core.config import TTSConfig


class TTSEngine:
    """
    Text-to-Speech engine with emotion awareness and multilingual support.
    
    Features:
    - Multiple TTS backends (gTTS, offline TTS)
    - Emotion-aware speech synthesis
    - Multilingual support
    - Real-time speech queue management
    - Offline capability
    """
    
    def __init__(self, config: TTSConfig):
        """
        Initialize the TTS engine.
        
        Args:
            config: TTS configuration object
        """
        self.config = config
        self.is_speaking = False
        self.speech_queue = queue.Queue()
        self.speech_thread = None
        self.stop_speaking = False
        
        # Initialize pygame mixer for audio playback
        if AUDIO_AVAILABLE:
            try:
                pygame.mixer.init()
                self.audio_initialized = True
            except Exception as e:
                logger.error(f"Failed to initialize audio: {e}")
                self.audio_initialized = False
        else:
            self.audio_initialized = False
        
        # Emotion detection patterns (simple keyword-based)
        self.emotion_patterns = {
            'happy': ['good', 'great', 'excellent', 'wonderful', 'amazing', 'fantastic'],
            'sad': ['bad', 'terrible', 'awful', 'horrible', 'disappointed'],
            'excited': ['wow', 'incredible', 'awesome', 'brilliant', 'outstanding'],
            'calm': ['okay', 'fine', 'normal', 'regular', 'standard']
        }
        
        # Start speech processing thread
        self._start_speech_thread()
        
        logger.info("TTSEngine initialized")
    
    def speak(self, text: str, language: str = None, emotion: str = None) -> bool:
        """
        Convert text to speech and play it.
        
        Args:
            text: Text to synthesize
            language: Language code (if None, uses config default)
            emotion: Emotion for speech synthesis (optional)
            
        Returns:
            True if speech was queued successfully, False otherwise
        """
        if not text.strip():
            return False
        
        try:
            # Use default language if not specified
            lang = language or self.config.language
            
            # Detect emotion if not provided and emotion detection is enabled
            if emotion is None and self.config.emotion_detection:
                emotion = self._detect_emotion(text)
            
            # Add to speech queue
            speech_item = {
                'text': text,
                'language': lang,
                'emotion': emotion,
                'timestamp': time.time()
            }
            
            self.speech_queue.put(speech_item)
            logger.debug(f"Added to speech queue: {text[:50]}...")
            return True
            
        except Exception as e:
            logger.error(f"Error queuing speech: {e}")
            return False
    
    def speak_immediately(self, text: str, language: str = None) -> bool:
        """
        Speak text immediately, bypassing the queue.
        
        Args:
            text: Text to synthesize
            language: Language code (if None, uses config default)
            
        Returns:
            True if speech was successful, False otherwise
        """
        if not AUDIO_AVAILABLE or not self.audio_initialized:
            logger.warning("Audio not available for immediate speech")
            return False
        
        try:
            lang = language or self.config.language
            return self._synthesize_and_play(text, lang)
            
        except Exception as e:
            logger.error(f"Error in immediate speech: {e}")
            return False
    
    def stop_current_speech(self) -> None:
        """Stop current speech playback."""
        self.stop_speaking = True
        
        if AUDIO_AVAILABLE and self.audio_initialized:
            try:
                pygame.mixer.stop()
            except Exception as e:
                logger.error(f"Error stopping speech: {e}")
    
    def clear_speech_queue(self) -> None:
        """Clear all pending speech items."""
        while not self.speech_queue.empty():
            try:
                self.speech_queue.get_nowait()
            except queue.Empty:
                break
        
        logger.info("Speech queue cleared")
    
    def set_voice_speed(self, speed: float) -> None:
        """
        Set voice speed.
        
        Args:
            speed: Speed multiplier (1.0 = normal, 0.5 = slow, 2.0 = fast)
        """
        self.config.voice_speed = max(0.1, min(3.0, speed))
        logger.info(f"Voice speed set to {self.config.voice_speed}")
    
    def _detect_emotion(self, text: str) -> str:
        """
        Detect emotion from text using simple keyword matching.
        
        Args:
            text: Input text
            
        Returns:
            Detected emotion or 'neutral'
        """
        text_lower = text.lower()
        
        for emotion, keywords in self.emotion_patterns.items():
            if any(keyword in text_lower for keyword in keywords):
                return emotion
        
        return 'neutral'
    
    def _synthesize_and_play(self, text: str, language: str, emotion: str = None) -> bool:
        """
        Synthesize text to speech and play it.
        
        Args:
            text: Text to synthesize
            language: Language code
            emotion: Emotion for synthesis
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create gTTS object
            tts = gTTS(
                text=text,
                lang=language,
                slow=self.config.slow_speech
            )
            
            # Save to temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.mp3') as tmp_file:
                tts.save(tmp_file.name)
                
                # Load and play audio
                if self.audio_initialized:
                    # Apply emotion and speed modifications if needed
                    audio_file = self._apply_audio_effects(tmp_file.name, emotion)
                    
                    # Play audio
                    pygame.mixer.music.load(audio_file)
                    pygame.mixer.music.play()
                    
                    # Wait for playback to complete
                    while pygame.mixer.music.get_busy() and not self.stop_speaking:
                        time.sleep(0.1)
                    
                    # Cleanup
                    os.unlink(tmp_file.name)
                    if audio_file != tmp_file.name:
                        os.unlink(audio_file)
                    
                    return True
                else:
                    logger.warning("Audio not initialized, cannot play speech")
                    os.unlink(tmp_file.name)
                    return False
                
        except Exception as e:
            logger.error(f"Error in speech synthesis: {e}")
            return False
    
    def _apply_audio_effects(self, audio_file: str, emotion: str = None) -> str:
        """
        Apply audio effects based on emotion and speed settings.
        
        Args:
            audio_file: Path to audio file
            emotion: Emotion for effects
            
        Returns:
            Path to processed audio file
        """
        try:
            # Load audio
            audio = AudioSegment.from_mp3(audio_file)
            
            # Apply speed change
            if self.config.voice_speed != 1.0:
                # Change speed without changing pitch
                audio = audio.speedup(playback_speed=self.config.voice_speed)
            
            # Apply emotion-based effects
            if emotion:
                if emotion == 'happy':
                    # Slightly higher pitch and faster
                    audio = audio._spawn(audio.raw_data, overrides={
                        "frame_rate": int(audio.frame_rate * 1.1)
                    })
                elif emotion == 'sad':
                    # Lower pitch and slower
                    audio = audio._spawn(audio.raw_data, overrides={
                        "frame_rate": int(audio.frame_rate * 0.9)
                    })
                elif emotion == 'excited':
                    # Higher pitch and volume
                    audio = audio._spawn(audio.raw_data, overrides={
                        "frame_rate": int(audio.frame_rate * 1.2)
                    })
                    audio = audio + 3  # Increase volume by 3dB
            
            # Save processed audio
            with tempfile.NamedTemporaryFile(delete=False, suffix='.mp3') as tmp_file:
                audio.export(tmp_file.name, format='mp3')
                return tmp_file.name
                
        except Exception as e:
            logger.error(f"Error applying audio effects: {e}")
            return audio_file  # Return original file if processing fails
    
    def _start_speech_thread(self) -> None:
        """Start the speech processing thread."""
        self.speech_thread = threading.Thread(
            target=self._speech_processing_loop,
            daemon=True
        )
        self.speech_thread.start()
    
    def _speech_processing_loop(self) -> None:
        """Main speech processing loop."""
        while True:
            try:
                # Get speech item from queue (blocking)
                speech_item = self.speech_queue.get(timeout=1.0)
                
                if speech_item is None:  # Shutdown signal
                    break
                
                # Set speaking flag
                self.is_speaking = True
                self.stop_speaking = False
                
                # Synthesize and play
                self._synthesize_and_play(
                    speech_item['text'],
                    speech_item['language'],
                    speech_item.get('emotion')
                )
                
                # Clear speaking flag
                self.is_speaking = False
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Error in speech processing loop: {e}")
                self.is_speaking = False
    
    def get_supported_languages(self) -> List[str]:
        """
        Get list of supported languages.
        
        Returns:
            List of language codes
        """
        return self.config.supported_languages.copy()
    
    def is_language_supported(self, language: str) -> bool:
        """
        Check if a language is supported.
        
        Args:
            language: Language code
            
        Returns:
            True if supported, False otherwise
        """
        return language in self.config.supported_languages
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get TTS engine status.
        
        Returns:
            Dictionary with status information
        """
        return {
            'is_speaking': self.is_speaking,
            'queue_size': self.speech_queue.qsize(),
            'audio_initialized': self.audio_initialized,
            'current_language': self.config.language,
            'voice_speed': self.config.voice_speed,
            'emotion_detection': self.config.emotion_detection
        }
    
    def shutdown(self) -> None:
        """Shutdown the TTS engine."""
        # Stop current speech
        self.stop_current_speech()
        
        # Clear queue
        self.clear_speech_queue()
        
        # Signal shutdown to thread
        self.speech_queue.put(None)
        
        # Wait for thread to finish
        if self.speech_thread and self.speech_thread.is_alive():
            self.speech_thread.join(timeout=2.0)
        
        # Cleanup pygame
        if AUDIO_AVAILABLE and self.audio_initialized:
            try:
                pygame.mixer.quit()
            except Exception:
                pass
        
        logger.info("TTSEngine shutdown complete")
    
    def __del__(self):
        """Cleanup on destruction."""
        self.shutdown()
