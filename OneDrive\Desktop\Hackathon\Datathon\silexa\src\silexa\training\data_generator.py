"""
Training data generator for lip reading models
"""

import numpy as np
import cv2
import os
import json
from typing import List, Tu<PERSON>, Dict, Optional, Generator
import random
from pathlib import Path
from loguru import logger

from ..core.config import SilexaConfig
from ..feature_extraction.lip_extractor import LipFeatureExtractor
from ..data_collection.video_processor import VideoProcessor


class LipReadingDataGenerator:
    """
    Generates training data for lip reading models.
    
    Can work with:
    1. Real video datasets
    2. Synthetic data for testing
    3. Webcam data collection
    """
    
    def __init__(self, config: SilexaConfig):
        """Initialize the data generator"""
        self.config = config
        self.lip_extractor = LipFeatureExtractor(config.model)
        self.video_processor = VideoProcessor(config.video)
        
        # Vocabulary for synthetic data
        self.basic_vocabulary = [
            "hello", "world", "yes", "no", "please", "thank", "you", "good", "bad",
            "help", "water", "food", "home", "work", "family", "friend", "love",
            "happy", "sad", "angry", "tired", "hungry", "thirsty", "cold", "hot",
            "big", "small", "fast", "slow", "up", "down", "left", "right",
            "one", "two", "three", "four", "five", "six", "seven", "eight", "nine", "ten"
        ]
        
        # Create word to index mapping
        self.word_to_idx = {word: idx for idx, word in enumerate(self.basic_vocabulary)}
        self.idx_to_word = {idx: word for word, idx in self.word_to_idx.items()}
        self.vocab_size = len(self.basic_vocabulary)
        
        logger.info(f"DataGenerator initialized with vocabulary size: {self.vocab_size}")
    
    def generate_synthetic_data(self, num_samples: int = 1000) -> Tuple[np.ndarray, np.ndarray]:
        """
        Generate synthetic training data for testing the pipeline.
        
        Args:
            num_samples: Number of samples to generate
            
        Returns:
            Tuple of (features, labels)
        """
        logger.info(f"Generating {num_samples} synthetic samples...")
        
        feature_dim = self.lip_extractor.get_feature_dimension()
        sequence_length = self.config.model.sequence_length
        
        # Generate random features that simulate lip movements
        X = []
        y = []
        
        for i in range(num_samples):
            # Generate a sequence of features
            sequence = []
            
            # Choose a random word
            word_idx = random.randint(0, self.vocab_size - 1)
            word = self.idx_to_word[word_idx]
            
            # Generate features that vary based on the word
            # This is synthetic but creates patterns the model can learn
            base_pattern = self._generate_word_pattern(word)
            
            for frame in range(sequence_length):
                # Create frame features with some variation
                frame_features = base_pattern + np.random.normal(0, 0.1, feature_dim)
                
                # Add temporal variation
                temporal_factor = np.sin(2 * np.pi * frame / sequence_length)
                frame_features += temporal_factor * 0.2
                
                sequence.append(frame_features)
            
            X.append(sequence)
            y.append(word_idx)
            
            if (i + 1) % 100 == 0:
                logger.info(f"Generated {i + 1}/{num_samples} samples")
        
        X = np.array(X, dtype=np.float32)
        y = np.array(y, dtype=np.int32)
        
        logger.info(f"Synthetic data generated: X shape {X.shape}, y shape {y.shape}")
        return X, y
    
    def _generate_word_pattern(self, word: str) -> np.ndarray:
        """Generate a characteristic pattern for a word"""
        feature_dim = self.lip_extractor.get_feature_dimension()
        
        # Create patterns based on word characteristics
        pattern = np.zeros(feature_dim)
        
        # Mouth opening patterns based on vowels
        vowel_count = sum(1 for char in word.lower() if char in 'aeiou')
        pattern[0] = vowel_count * 0.3  # Mouth height
        
        # Lip spreading based on certain letters
        spread_letters = sum(1 for char in word.lower() if char in 'eiy')
        pattern[1] = spread_letters * 0.2  # Mouth width
        
        # Lip rounding based on certain letters
        round_letters = sum(1 for char in word.lower() if char in 'ou')
        pattern[2] = round_letters * 0.25  # Lip rounding
        
        # Add word-specific signature
        word_hash = hash(word) % 1000
        pattern[3:6] = [(word_hash % 100) / 100.0, 
                       ((word_hash // 100) % 10) / 10.0,
                       (word_hash // 1000) / 10.0]
        
        # Fill remaining features with word-derived patterns
        for i in range(6, feature_dim):
            pattern[i] = np.sin(word_hash * i / feature_dim) * 0.1
        
        return pattern
    
    def collect_webcam_data(self, word: str, num_sequences: int = 5) -> List[np.ndarray]:
        """
        Collect training data from webcam for a specific word.
        
        Args:
            word: Word to collect data for
            num_sequences: Number of sequences to collect
            
        Returns:
            List of feature sequences
        """
        if word not in self.word_to_idx:
            logger.error(f"Word '{word}' not in vocabulary")
            return []
        
        logger.info(f"Collecting data for word: '{word}'")
        logger.info(f"Please say the word '{word}' {num_sequences} times when prompted")
        
        sequences = []
        
        # Start video capture
        if not self.video_processor.start_capture(0):
            logger.error("Failed to start video capture")
            return []
        
        try:
            for seq_num in range(num_sequences):
                logger.info(f"Recording sequence {seq_num + 1}/{num_sequences} for '{word}'")
                logger.info("Press SPACE to start recording, ESC to skip...")
                
                # Wait for user input
                while True:
                    frame = self.video_processor.get_frame()
                    if frame is not None:
                        # Display frame
                        display_frame = (frame * 255).astype(np.uint8)
                        cv2.putText(display_frame, f"Say '{word}' - Press SPACE to record", 
                                  (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                        cv2.imshow("Data Collection", display_frame)
                    
                    key = cv2.waitKey(1) & 0xFF
                    if key == ord(' '):  # Space to start
                        break
                    elif key == 27:  # ESC to skip
                        logger.info("Sequence skipped")
                        continue
                
                # Record sequence
                sequence = self._record_sequence()
                if sequence is not None:
                    sequences.append(sequence)
                    logger.info(f"Sequence {seq_num + 1} recorded successfully")
                else:
                    logger.warning(f"Failed to record sequence {seq_num + 1}")
        
        finally:
            self.video_processor.stop_capture()
            cv2.destroyAllWindows()
        
        logger.info(f"Collected {len(sequences)} sequences for '{word}'")
        return sequences
    
    def _record_sequence(self) -> Optional[np.ndarray]:
        """Record a single sequence of lip movements"""
        sequence = []
        frames_recorded = 0
        target_frames = self.config.model.sequence_length
        
        logger.info(f"Recording {target_frames} frames...")
        
        while frames_recorded < target_frames:
            frame = self.video_processor.get_frame()
            
            if frame is not None:
                # Extract features
                features = self.lip_extractor.extract_features(frame)
                
                if features is not None:
                    sequence.append(features)
                    frames_recorded += 1
                    
                    # Show progress
                    display_frame = (frame * 255).astype(np.uint8)
                    progress = f"Recording: {frames_recorded}/{target_frames}"
                    cv2.putText(display_frame, progress, (10, 30), 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                    cv2.imshow("Data Collection", display_frame)
                    cv2.waitKey(1)
        
        if len(sequence) == target_frames:
            return np.array(sequence)
        else:
            logger.warning(f"Incomplete sequence: {len(sequence)}/{target_frames}")
            return None
    
    def save_dataset(self, X: np.ndarray, y: np.ndarray, dataset_name: str) -> str:
        """
        Save dataset to disk.
        
        Args:
            X: Feature sequences
            y: Labels
            dataset_name: Name for the dataset
            
        Returns:
            Path to saved dataset
        """
        dataset_dir = Path(self.config.data.processed_data_dir) / dataset_name
        dataset_dir.mkdir(parents=True, exist_ok=True)
        
        # Save features and labels
        np.save(dataset_dir / "features.npy", X)
        np.save(dataset_dir / "labels.npy", y)
        
        # Save vocabulary
        vocab_info = {
            "vocabulary": self.basic_vocabulary,
            "word_to_idx": self.word_to_idx,
            "vocab_size": self.vocab_size,
            "feature_dim": X.shape[-1],
            "sequence_length": X.shape[1],
            "num_samples": len(X)
        }
        
        with open(dataset_dir / "vocab.json", "w") as f:
            json.dump(vocab_info, f, indent=2)
        
        logger.info(f"Dataset saved to {dataset_dir}")
        return str(dataset_dir)
    
    def load_dataset(self, dataset_path: str) -> Tuple[np.ndarray, np.ndarray, Dict]:
        """
        Load dataset from disk.
        
        Args:
            dataset_path: Path to dataset directory
            
        Returns:
            Tuple of (features, labels, vocab_info)
        """
        dataset_dir = Path(dataset_path)
        
        # Load features and labels
        X = np.load(dataset_dir / "features.npy")
        y = np.load(dataset_dir / "labels.npy")
        
        # Load vocabulary
        with open(dataset_dir / "vocab.json", "r") as f:
            vocab_info = json.load(f)
        
        logger.info(f"Dataset loaded from {dataset_dir}")
        logger.info(f"Features shape: {X.shape}, Labels shape: {y.shape}")
        
        return X, y, vocab_info
    
    def create_train_val_split(self, X: np.ndarray, y: np.ndarray, 
                              val_split: float = 0.2) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """
        Split data into training and validation sets.
        
        Args:
            X: Features
            y: Labels
            val_split: Fraction for validation
            
        Returns:
            Tuple of (X_train, X_val, y_train, y_val)
        """
        # Shuffle data
        indices = np.random.permutation(len(X))
        X_shuffled = X[indices]
        y_shuffled = y[indices]
        
        # Split
        split_idx = int(len(X) * (1 - val_split))
        
        X_train = X_shuffled[:split_idx]
        X_val = X_shuffled[split_idx:]
        y_train = y_shuffled[:split_idx]
        y_val = y_shuffled[split_idx:]
        
        logger.info(f"Data split - Train: {len(X_train)}, Val: {len(X_val)}")
        
        return X_train, X_val, y_train, y_val
    
    def get_vocabulary(self) -> Dict[str, int]:
        """Get the vocabulary mapping"""
        return self.word_to_idx.copy()
    
    def get_vocab_size(self) -> int:
        """Get vocabulary size"""
        return self.vocab_size
