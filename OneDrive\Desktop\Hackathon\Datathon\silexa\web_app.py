"""
Silexa Web Application - Run Silexa in your browser
"""

from flask import Flask, render_template, jsonify, request, send_from_directory
import os
import json
import time
import random
import threading
from datetime import datetime

app = Flask(__name__)

# Mock Silexa system for web demo
class WebSilexaDemo:
    def __init__(self):
        self.vocabulary = [
            "hello", "world", "yes", "no", "please", "thank", "you", "good", "bad",
            "help", "water", "food", "home", "work", "family", "friend", "love",
            "happy", "sad", "angry", "tired", "hungry", "thirsty", "cold", "hot"
        ]
        self.is_trained = True
        self.is_running = False
        self.current_prediction = {"text": "", "confidence": 0.0}
        self.training_progress = 0
        
    def predict_word(self):
        """Simulate lip reading prediction"""
        word = random.choice(self.vocabulary)
        confidence = random.uniform(0.75, 0.95)
        return word, confidence
    
    def detect_emotion(self, text):
        """Detect emotion from text"""
        emotions = {
            'happy': ['good', 'great', 'excellent', 'wonderful', 'amazing', 'love', 'happy'],
            'sad': ['bad', 'terrible', 'awful', 'horrible', 'sad'],
            'excited': ['wow', 'incredible', 'awesome', 'brilliant'],
            'calm': ['okay', 'fine', 'normal', 'regular', 'water', 'home']
        }
        
        text_lower = text.lower()
        for emotion, keywords in emotions.items():
            if any(keyword in text_lower for keyword in keywords):
                return emotion
        return 'neutral'

# Global demo instance
silexa_demo = WebSilexaDemo()

@app.route('/')
def index():
    """Main page"""
    return render_template('index.html')

@app.route('/api/status')
def get_status():
    """Get system status"""
    return jsonify({
        'is_trained': silexa_demo.is_trained,
        'is_running': silexa_demo.is_running,
        'vocabulary_size': len(silexa_demo.vocabulary),
        'current_prediction': silexa_demo.current_prediction
    })

@app.route('/api/start_recognition', methods=['POST'])
def start_recognition():
    """Start lip reading recognition"""
    silexa_demo.is_running = True
    return jsonify({'success': True, 'message': 'Recognition started'})

@app.route('/api/stop_recognition', methods=['POST'])
def stop_recognition():
    """Stop lip reading recognition"""
    silexa_demo.is_running = False
    return jsonify({'success': True, 'message': 'Recognition stopped'})

@app.route('/api/predict')
def get_prediction():
    """Get current prediction"""
    if silexa_demo.is_running:
        word, confidence = silexa_demo.predict_word()
        emotion = silexa_demo.detect_emotion(word)
        
        silexa_demo.current_prediction = {
            'text': word,
            'confidence': confidence,
            'emotion': emotion,
            'timestamp': datetime.now().isoformat()
        }
    
    return jsonify(silexa_demo.current_prediction)

@app.route('/api/speak', methods=['POST'])
def speak_text():
    """Simulate text-to-speech"""
    data = request.get_json()
    text = data.get('text', '')
    language = data.get('language', 'en')
    
    emotion = silexa_demo.detect_emotion(text)
    
    return jsonify({
        'success': True,
        'text': text,
        'language': language,
        'emotion': emotion,
        'message': f'Speaking "{text}" in {language} with {emotion} emotion'
    })

@app.route('/api/train', methods=['POST'])
def train_model():
    """Simulate model training"""
    data = request.get_json()
    samples = data.get('samples', 1000)
    epochs = data.get('epochs', 20)
    
    def training_simulation():
        silexa_demo.training_progress = 0
        for epoch in range(epochs):
            time.sleep(0.1)  # Simulate training time
            silexa_demo.training_progress = int((epoch + 1) / epochs * 100)
        silexa_demo.is_trained = True
    
    # Start training in background
    threading.Thread(target=training_simulation, daemon=True).start()
    
    return jsonify({
        'success': True,
        'message': f'Training started with {samples} samples and {epochs} epochs'
    })

@app.route('/api/training_progress')
def get_training_progress():
    """Get training progress"""
    return jsonify({
        'progress': silexa_demo.training_progress,
        'is_complete': silexa_demo.training_progress >= 100
    })

@app.route('/api/vocabulary')
def get_vocabulary():
    """Get current vocabulary"""
    return jsonify({
        'vocabulary': silexa_demo.vocabulary,
        'size': len(silexa_demo.vocabulary)
    })

# Create templates directory and HTML file
@app.before_first_request
def create_templates():
    """Create templates directory and files"""
    templates_dir = os.path.join(os.path.dirname(__file__), 'templates')
    os.makedirs(templates_dir, exist_ok=True)
    
    # Create the main HTML template
    html_content = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Silexa - AI-Powered Silent Speech Interpreter</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { 
            text-align: center; 
            color: white; 
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .header h1 { font-size: 3em; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .header p { font-size: 1.2em; opacity: 0.9; }
        .main-content { 
            display: grid; 
            grid-template-columns: 1fr 1fr; 
            gap: 20px; 
            margin-bottom: 20px;
        }
        .card { 
            background: white; 
            padding: 25px; 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }
        .card:hover { transform: translateY(-5px); }
        .card h2 { color: #667eea; margin-bottom: 20px; font-size: 1.5em; }
        .status-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 20px; }
        .status-item { 
            padding: 10px; 
            background: #f8f9fa; 
            border-radius: 8px; 
            text-align: center;
            border-left: 4px solid #667eea;
        }
        .status-item.active { background: #d4edda; border-left-color: #28a745; }
        .btn { 
            background: #667eea; 
            color: white; 
            border: none; 
            padding: 12px 24px; 
            border-radius: 8px; 
            cursor: pointer; 
            font-size: 1em;
            transition: all 0.3s ease;
            margin: 5px;
        }
        .btn:hover { background: #5a6fd8; transform: translateY(-2px); }
        .btn:disabled { background: #ccc; cursor: not-allowed; transform: none; }
        .btn.danger { background: #dc3545; }
        .btn.danger:hover { background: #c82333; }
        .prediction-display { 
            background: #f8f9fa; 
            padding: 20px; 
            border-radius: 10px; 
            margin: 15px 0;
            border-left: 5px solid #667eea;
        }
        .prediction-text { font-size: 1.5em; font-weight: bold; color: #667eea; margin-bottom: 10px; }
        .confidence-bar { 
            background: #e9ecef; 
            height: 10px; 
            border-radius: 5px; 
            overflow: hidden;
            margin: 10px 0;
        }
        .confidence-fill { 
            background: linear-gradient(90deg, #28a745, #20c997); 
            height: 100%; 
            transition: width 0.5s ease;
        }
        .progress-bar { 
            background: #e9ecef; 
            height: 20px; 
            border-radius: 10px; 
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill { 
            background: linear-gradient(90deg, #667eea, #764ba2); 
            height: 100%; 
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .vocabulary-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr)); 
            gap: 10px; 
            margin-top: 15px;
        }
        .vocab-word { 
            background: #f8f9fa; 
            padding: 8px; 
            border-radius: 5px; 
            text-align: center;
            border: 1px solid #dee2e6;
            transition: all 0.3s ease;
        }
        .vocab-word:hover { background: #e9ecef; transform: scale(1.05); }
        .full-width { grid-column: 1 / -1; }
        .demo-section { 
            background: white; 
            padding: 25px; 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-top: 20px;
        }
        .feature-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 15px; 
            margin-top: 20px;
        }
        .feature-card { 
            background: #f8f9fa; 
            padding: 20px; 
            border-radius: 10px; 
            text-align: center;
            border-top: 4px solid #667eea;
        }
        .feature-icon { font-size: 2em; margin-bottom: 10px; }
        @media (max-width: 768px) {
            .main-content { grid-template-columns: 1fr; }
            .header h1 { font-size: 2em; }
            .status-grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 SILEXA</h1>
            <p>AI-Powered Silent Speech Interpreter</p>
            <p><em>"Giving Voice to the Voiceless"</em></p>
        </div>

        <div class="main-content">
            <div class="card">
                <h2>🎤 Live Recognition</h2>
                <div class="status-grid">
                    <div class="status-item" id="training-status">
                        <strong>Model Status</strong><br>
                        <span id="model-status">Loading...</span>
                    </div>
                    <div class="status-item" id="recognition-status">
                        <strong>Recognition</strong><br>
                        <span id="recognition-state">Stopped</span>
                    </div>
                </div>
                
                <div class="prediction-display">
                    <div class="prediction-text" id="predicted-text">Ready to start...</div>
                    <div>Confidence: <span id="confidence-value">0%</span></div>
                    <div class="confidence-bar">
                        <div class="confidence-fill" id="confidence-bar" style="width: 0%"></div>
                    </div>
                    <div>Emotion: <span id="emotion-value">neutral</span></div>
                </div>

                <button class="btn" id="start-btn" onclick="toggleRecognition()">Start Recognition</button>
                <button class="btn" onclick="speakCurrent()">🔊 Speak Text</button>
                <button class="btn" onclick="clearPrediction()">Clear</button>
            </div>

            <div class="card">
                <h2>🧠 Model Training</h2>
                <div>
                    <label>Samples: <input type="number" id="samples" value="1000" min="100" max="5000"></label><br><br>
                    <label>Epochs: <input type="number" id="epochs" value="20" min="5" max="100"></label><br><br>
                </div>
                
                <div id="training-progress" style="display: none;">
                    <div>Training Progress:</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill" style="width: 0%">0%</div>
                    </div>
                </div>

                <button class="btn" onclick="startTraining()">🚀 Quick Train</button>
                <div id="training-message" style="margin-top: 10px; font-style: italic;"></div>
            </div>
        </div>

        <div class="demo-section">
            <h2>📋 Vocabulary</h2>
            <p>Current vocabulary size: <span id="vocab-size">0</span> words</p>
            <div class="vocabulary-grid" id="vocabulary-grid">
                <!-- Vocabulary words will be loaded here -->
            </div>
        </div>

        <div class="demo-section">
            <h2>🌟 Key Features</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">👄</div>
                    <h3>Real-time Lip Reading</h3>
                    <p>Advanced computer vision to interpret lip movements</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🧠</div>
                    <h3>AI-Powered</h3>
                    <p>Deep learning models with 90%+ accuracy</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔊</div>
                    <h3>Text-to-Speech</h3>
                    <p>Natural speech synthesis with emotion detection</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🌍</div>
                    <h3>Multilingual</h3>
                    <p>Support for multiple languages</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h3>Real-time</h3>
                    <p>Low latency processing for live communication</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔒</div>
                    <h3>Offline Capable</h3>
                    <p>Works without internet connection</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isRecognitionRunning = false;
        let recognitionInterval;
        let trainingInterval;

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus();
            loadVocabulary();
            setInterval(updateStatus, 2000); // Update status every 2 seconds
        });

        async function updateStatus() {
            try {
                const response = await fetch('/api/status');
                const status = await response.json();
                
                document.getElementById('model-status').textContent = status.is_trained ? 'Trained ✅' : 'Not Trained ❌';
                document.getElementById('recognition-state').textContent = status.is_running ? 'Running ✅' : 'Stopped ⏹️';
                
                // Update status indicators
                const trainingStatus = document.getElementById('training-status');
                const recognitionStatus = document.getElementById('recognition-status');
                
                if (status.is_trained) trainingStatus.classList.add('active');
                else trainingStatus.classList.remove('active');
                
                if (status.is_running) recognitionStatus.classList.add('active');
                else recognitionStatus.classList.remove('active');
                
            } catch (error) {
                console.error('Error updating status:', error);
            }
        }

        async function toggleRecognition() {
            const startBtn = document.getElementById('start-btn');
            
            if (!isRecognitionRunning) {
                // Start recognition
                try {
                    await fetch('/api/start_recognition', { method: 'POST' });
                    isRecognitionRunning = true;
                    startBtn.textContent = 'Stop Recognition';
                    startBtn.classList.add('danger');
                    
                    // Start getting predictions
                    recognitionInterval = setInterval(updatePrediction, 1500);
                } catch (error) {
                    console.error('Error starting recognition:', error);
                }
            } else {
                // Stop recognition
                try {
                    await fetch('/api/stop_recognition', { method: 'POST' });
                    isRecognitionRunning = false;
                    startBtn.textContent = 'Start Recognition';
                    startBtn.classList.remove('danger');
                    
                    clearInterval(recognitionInterval);
                } catch (error) {
                    console.error('Error stopping recognition:', error);
                }
            }
        }

        async function updatePrediction() {
            if (!isRecognitionRunning) return;
            
            try {
                const response = await fetch('/api/predict');
                const prediction = await response.json();
                
                if (prediction.text) {
                    document.getElementById('predicted-text').textContent = prediction.text;
                    document.getElementById('confidence-value').textContent = Math.round(prediction.confidence * 100) + '%';
                    document.getElementById('confidence-bar').style.width = (prediction.confidence * 100) + '%';
                    document.getElementById('emotion-value').textContent = prediction.emotion || 'neutral';
                }
            } catch (error) {
                console.error('Error getting prediction:', error);
            }
        }

        async function speakCurrent() {
            const text = document.getElementById('predicted-text').textContent;
            if (!text || text === 'Ready to start...') return;
            
            try {
                const response = await fetch('/api/speak', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ text: text, language: 'en' })
                });
                const result = await response.json();
                
                if (result.success) {
                    // Visual feedback
                    const btn = event.target;
                    const originalText = btn.textContent;
                    btn.textContent = '🔊 Speaking...';
                    btn.disabled = true;
                    
                    setTimeout(() => {
                        btn.textContent = originalText;
                        btn.disabled = false;
                    }, 2000);
                }
            } catch (error) {
                console.error('Error speaking text:', error);
            }
        }

        function clearPrediction() {
            document.getElementById('predicted-text').textContent = 'Ready to start...';
            document.getElementById('confidence-value').textContent = '0%';
            document.getElementById('confidence-bar').style.width = '0%';
            document.getElementById('emotion-value').textContent = 'neutral';
        }

        async function startTraining() {
            const samples = document.getElementById('samples').value;
            const epochs = document.getElementById('epochs').value;
            
            try {
                const response = await fetch('/api/train', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ samples: parseInt(samples), epochs: parseInt(epochs) })
                });
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('training-message').textContent = result.message;
                    document.getElementById('training-progress').style.display = 'block';
                    
                    // Monitor training progress
                    trainingInterval = setInterval(updateTrainingProgress, 500);
                }
            } catch (error) {
                console.error('Error starting training:', error);
            }
        }

        async function updateTrainingProgress() {
            try {
                const response = await fetch('/api/training_progress');
                const progress = await response.json();
                
                const progressFill = document.getElementById('progress-fill');
                progressFill.style.width = progress.progress + '%';
                progressFill.textContent = progress.progress + '%';
                
                if (progress.is_complete) {
                    clearInterval(trainingInterval);
                    document.getElementById('training-message').textContent = 'Training completed successfully! ✅';
                    setTimeout(() => {
                        document.getElementById('training-progress').style.display = 'none';
                    }, 3000);
                }
            } catch (error) {
                console.error('Error getting training progress:', error);
            }
        }

        async function loadVocabulary() {
            try {
                const response = await fetch('/api/vocabulary');
                const vocab = await response.json();
                
                document.getElementById('vocab-size').textContent = vocab.size;
                
                const grid = document.getElementById('vocabulary-grid');
                grid.innerHTML = '';
                
                vocab.vocabulary.forEach(word => {
                    const wordElement = document.createElement('div');
                    wordElement.className = 'vocab-word';
                    wordElement.textContent = word;
                    wordElement.onclick = () => {
                        document.getElementById('predicted-text').textContent = word;
                        document.getElementById('confidence-value').textContent = '95%';
                        document.getElementById('confidence-bar').style.width = '95%';
                    };
                    grid.appendChild(wordElement);
                });
            } catch (error) {
                console.error('Error loading vocabulary:', error);
            }
        }
    </script>
</body>
</html>'''
    
    with open(os.path.join(templates_dir, 'index.html'), 'w', encoding='utf-8') as f:
        f.write(html_content)

if __name__ == '__main__':
    print("🌐 Starting Silexa Web Application...")
    print("📱 Open your browser and go to: http://localhost:5000")
    print("🎯 Silexa will be running as a website!")
    print("\n✨ Features available in web version:")
    print("  • Live lip reading simulation")
    print("  • AI model training")
    print("  • Text-to-speech")
    print("  • Interactive vocabulary")
    print("  • Real-time predictions")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
