"""
Installation script for Silexa
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed:")
        print(f"Error: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible"""
    print("Checking Python version...")
    version = sys.version_info
    
    if version.major == 3 and version.minor >= 8:
        print(f"✓ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        print(f"✗ Python {version.major}.{version.minor}.{version.micro} is not compatible")
        print("Silexa requires Python 3.8 or higher")
        return False


def install_dependencies():
    """Install required dependencies"""
    print("\nInstalling dependencies...")
    
    # Core dependencies
    core_deps = [
        "numpy>=1.21.0",
        "opencv-python>=4.5.0",
        "mediapipe>=0.8.10",
        "tensorflow>=2.8.0",
        "loguru>=0.5.3",
        "click>=8.0.0"
    ]
    
    # Optional dependencies (may fail on some systems)
    optional_deps = [
        "gTTS>=2.2.4",
        "pygame>=2.1.0",
        "pydub>=0.25.1"
    ]
    
    # Install core dependencies
    for dep in core_deps:
        if not run_command(f"pip install {dep}", f"Installing {dep}"):
            return False
    
    # Install optional dependencies (continue on failure)
    for dep in optional_deps:
        run_command(f"pip install {dep}", f"Installing {dep} (optional)")
    
    return True


def setup_project():
    """Set up the project structure"""
    print("\nSetting up project structure...")
    
    # Create necessary directories
    directories = [
        "data/raw",
        "data/processed", 
        "data/models",
        "logs",
        "outputs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✓ Created directory: {directory}")
    
    return True


def install_package():
    """Install the Silexa package in development mode"""
    return run_command("pip install -e .", "Installing Silexa package")


def run_tests():
    """Run basic tests to verify installation"""
    print("\nRunning installation tests...")
    
    try:
        # Test imports
        print("Testing imports...")
        import numpy
        import cv2
        import mediapipe
        import tensorflow
        print("✓ Core dependencies imported successfully")
        
        # Test Silexa imports
        sys.path.insert(0, "src")
        from silexa.core.config import SilexaConfig
        from silexa.core.interpreter import SilentSpeechInterpreter
        print("✓ Silexa modules imported successfully")
        
        # Test basic functionality
        config = SilexaConfig()
        interpreter = SilentSpeechInterpreter(config)
        print("✓ Silexa interpreter initialized successfully")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import test failed: {e}")
        return False
    except Exception as e:
        print(f"✗ Test failed: {e}")
        return False


def main():
    """Main installation function"""
    print("Silexa Installation Script")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n✗ Dependency installation failed")
        sys.exit(1)
    
    # Set up project
    if not setup_project():
        print("\n✗ Project setup failed")
        sys.exit(1)
    
    # Install package
    if not install_package():
        print("\n✗ Package installation failed")
        sys.exit(1)
    
    # Run tests
    if not run_tests():
        print("\n⚠ Installation completed but tests failed")
        print("Some features may not work correctly")
    else:
        print("\n✅ Installation completed successfully!")
    
    print("\n" + "=" * 40)
    print("Next steps:")
    print("1. Run basic example: python examples/basic_usage.py")
    print("2. Test CLI: python -m silexa.cli info")
    print("3. Start live mode: python -m silexa.cli live")
    print("4. Read documentation in README.md")


if __name__ == "__main__":
    main()
