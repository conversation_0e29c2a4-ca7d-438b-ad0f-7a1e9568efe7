Collecting mediapipe
  Downloading mediapipe-0.10.21-cp311-cp311-win_amd64.whl.metadata (10 kB)
Collecting absl-py (from mediapipe)
  Downloading absl_py-2.3.1-py3-none-any.whl.metadata (3.3 kB)
Requirement already satisfied: attrs>=19.1.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from mediapipe) (25.3.0)
Collecting flatbuffers>=2.0 (from mediapipe)
  Using cached flatbuffers-25.2.10-py2.py3-none-any.whl.metadata (875 bytes)
Collecting jax (from mediapipe)
  Downloading jax-0.7.0-py3-none-any.whl.metadata (13 kB)
Collecting jaxlib (from mediapipe)
  Downloading jaxlib-0.7.0-cp311-cp311-win_amd64.whl.metadata (1.3 kB)
Requirement already satisfied: matplotlib in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from mediapipe) (3.10.3)
Collecting numpy<2 (from mediapipe)
  Downloading numpy-1.26.4-cp311-cp311-win_amd64.whl.metadata (61 kB)
     -------------------------------------- 61.0/61.0 kB 651.3 kB/s eta 0:00:00
Collecting opencv-contrib-python (from mediapipe)
  Downloading opencv_contrib_python-*********-cp37-abi3-win_amd64.whl.metadata (20 kB)
Collecting protobuf<5,>=4.25.3 (from mediapipe)
  Downloading protobuf-4.25.8-cp310-abi3-win_amd64.whl.metadata (541 bytes)
Collecting sounddevice>=0.4.4 (from mediapipe)
  Downloading sounddevice-0.5.2-py3-none-win_amd64.whl.metadata (1.6 kB)
Collecting sentencepiece (from mediapipe)
  Downloading sentencepiece-0.2.0-cp311-cp311-win_amd64.whl.metadata (8.3 kB)
Collecting CFFI>=1.0 (from sounddevice>=0.4.4->mediapipe)
  Downloading cffi-1.17.1-cp311-cp311-win_amd64.whl.metadata (1.6 kB)
Collecting ml_dtypes>=0.5.0 (from jax->mediapipe)
  Downloading ml_dtypes-0.5.3-cp311-cp311-win_amd64.whl.metadata (9.2 kB)
Collecting opt_einsum (from jax->mediapipe)
  Downloading opt_einsum-3.4.0-py3-none-any.whl.metadata (6.3 kB)
Collecting scipy>=1.12 (from jax->mediapipe)
  Downloading scipy-1.16.1-cp311-cp311-win_amd64.whl.metadata (60 kB)
     ---------------------------------------- 60.8/60.8 kB 1.6 MB/s eta 0:00:00
Requirement already satisfied: contourpy>=1.0.1 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from matplotlib->mediapipe) (1.3.2)
Requirement already satisfied: cycler>=0.10 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from matplotlib->mediapipe) (0.12.1)
Requirement already satisfied: fonttools>=4.22.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from matplotlib->mediapipe) (4.58.0)
Requirement already satisfied: kiwisolver>=1.3.1 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from matplotlib->mediapipe) (1.4.8)
Requirement already satisfied: packaging>=20.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from matplotlib->mediapipe) (25.0)
Requirement already satisfied: pillow>=8 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from matplotlib->mediapipe) (10.1.0)
Requirement already satisfied: pyparsing>=2.3.1 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from matplotlib->mediapipe) (3.2.3)
Requirement already satisfied: python-dateutil>=2.7 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from matplotlib->mediapipe) (2.8.2)
INFO: pip is looking at multiple versions of opencv-contrib-python to determine which version is compatible with other requirements. This could take a while.
Collecting opencv-contrib-python (from mediapipe)
  Downloading opencv_contrib_python-*********-cp37-abi3-win_amd64.whl.metadata (20 kB)
Collecting pycparser (from CFFI>=1.0->sounddevice>=0.4.4->mediapipe)
  Downloading pycparser-2.22-py3-none-any.whl.metadata (943 bytes)
Requirement already satisfied: six>=1.5 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from python-dateutil>=2.7->matplotlib->mediapipe) (1.17.0)
Downloading mediapipe-0.10.21-cp311-cp311-win_amd64.whl (51.0 MB)
   ---------------------------------------- 51.0/51.0 MB 1.4 MB/s eta 0:00:00
Using cached flatbuffers-25.2.10-py2.py3-none-any.whl (30 kB)
Downloading numpy-1.26.4-cp311-cp311-win_amd64.whl (15.8 MB)
   ---------------------------------------- 15.8/15.8 MB 5.4 MB/s eta 0:00:00
Downloading protobuf-4.25.8-cp310-abi3-win_amd64.whl (413 kB)
   ---------------------------------------- 413.7/413.7 kB 4.3 MB/s eta 0:00:00
Downloading sounddevice-0.5.2-py3-none-win_amd64.whl (363 kB)
   ---------------------------------------- 363.8/363.8 kB 4.5 MB/s eta 0:00:00
Downloading absl_py-2.3.1-py3-none-any.whl (135 kB)
   ---------------------------------------- 135.8/135.8 kB 3.9 MB/s eta 0:00:00
Downloading jax-0.7.0-py3-none-any.whl (2.8 MB)
   ---------------------------------------- 2.8/2.8 MB 7.4 MB/s eta 0:00:00
Downloading jaxlib-0.7.0-cp311-cp311-win_amd64.whl (60.2 MB)
   ---------------------------------------- 60.2/60.2 MB 1.5 MB/s eta 0:00:00
Downloading opencv_contrib_python-*********-cp37-abi3-win_amd64.whl (46.2 MB)
   ---------------------------------------- 46.2/46.2 MB 4.3 MB/s eta 0:00:00
Downloading sentencepiece-0.2.0-cp311-cp311-win_amd64.whl (991 kB)
   ---------------------------------------- 991.5/991.5 kB 5.2 MB/s eta 0:00:00
Downloading cffi-1.17.1-cp311-cp311-win_amd64.whl (181 kB)
   ---------------------------------------- 181.4/181.4 kB 5.5 MB/s eta 0:00:00
Downloading ml_dtypes-0.5.3-cp311-cp311-win_amd64.whl (206 kB)
   ---------------------------------------- 206.3/206.3 kB 3.1 MB/s eta 0:00:00
Downloading scipy-1.16.1-cp311-cp311-win_amd64.whl (38.6 MB)
   ---------------------------------------- 38.6/38.6 MB 2.7 MB/s eta 0:00:00
Downloading opt_einsum-3.4.0-py3-none-any.whl (71 kB)
   ---------------------------------------- 71.9/71.9 kB 3.9 MB/s eta 0:00:00
Downloading pycparser-2.22-py3-none-any.whl (117 kB)
   ---------------------------------------- 117.6/117.6 kB 1.7 MB/s eta 0:00:00
Installing collected packages: sentencepiece, flatbuffers, pycparser, protobuf, opt_einsum, numpy, absl-py, scipy, opencv-contrib-python, ml_dtypes, CFFI, sounddevice, jaxlib, jax, mediapipe
  Attempting uninstall: numpy
    Found existing installation: numpy 2.2.5
    Uninstalling numpy-2.2.5:
      Successfully uninstalled numpy-2.2.5
Successfully installed CFFI-1.17.1 absl-py-2.3.1 flatbuffers-25.2.10 jax-0.7.0 jaxlib-0.7.0 mediapipe-0.10.21 ml_dtypes-0.5.3 numpy-1.26.4 opencv-contrib-python-********* opt_einsum-3.4.0 protobuf-4.25.8 pycparser-2.22 scipy-1.16.1 sentencepiece-0.2.0 sounddevice-0.5.2
