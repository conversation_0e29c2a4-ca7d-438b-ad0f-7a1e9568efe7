"""
Lip feature extraction using MediaPipe face landmarks
"""

import cv2
import numpy as np
import mediapipe as mp
from typing import Optional, List, Tuple, Dict, Any
from loguru import logger

from ..core.config import ModelConfig


class LipFeatureExtractor:
    """
    Extracts lip movement features from video frames using MediaPipe.
    
    Features extracted:
    - Lip landmark coordinates (68 points)
    - Lip contour distances and angles
    - Mouth opening measurements
    - Temporal features (movement between frames)
    """
    
    # MediaPipe lip landmark indices
    LIP_LANDMARKS = [
        # Outer lip
        61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318,
        # Inner lip  
        78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308, 415
    ]
    
    # Specific landmark groups
    UPPER_LIP = [61, 84, 17, 314, 405, 320, 307, 375]
    LOWER_LIP = [78, 95, 88, 178, 87, 14, 317, 402]
    LIP_CORNERS = [61, 291]  # Left and right corners
    
    def __init__(self, config: ModelConfig):
        """
        Initialize the lip feature extractor.
        
        Args:
            config: Model configuration object
        """
        self.config = config
        
        # Initialize MediaPipe
        self.mp_face_mesh = mp.solutions.face_mesh
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            static_image_mode=False,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.5,
            min_tracking_confidence=0.5
        )
        
        # Feature history for temporal features
        self.previous_landmarks = None
        self.landmark_history = []
        self.max_history = 5
        
        # Statistics
        self.frames_processed = 0
        self.successful_extractions = 0
        
        logger.info("LipFeatureExtractor initialized")
    
    def extract_features(self, frame: np.ndarray) -> Optional[np.ndarray]:
        """
        Extract lip features from a single frame.
        
        Args:
            frame: Input frame (RGB format, normalized 0-1)
            
        Returns:
            Feature vector or None if extraction failed
        """
        try:
            self.frames_processed += 1
            
            # Convert to uint8 for MediaPipe
            if frame.dtype == np.float32:
                frame_uint8 = (frame * 255).astype(np.uint8)
            else:
                frame_uint8 = frame
            
            # Process with MediaPipe
            results = self.face_mesh.process(frame_uint8)
            
            if not results.multi_face_landmarks:
                return None
            
            # Get face landmarks
            face_landmarks = results.multi_face_landmarks[0]
            
            # Extract lip landmarks
            lip_landmarks = self._extract_lip_landmarks(face_landmarks, frame.shape)
            
            if lip_landmarks is None:
                return None
            
            # Compute features
            features = self._compute_features(lip_landmarks)
            
            # Update history
            self._update_history(lip_landmarks)
            
            self.successful_extractions += 1
            return features
            
        except Exception as e:
            logger.error(f"Error extracting features: {e}")
            return None
    
    def _extract_lip_landmarks(self, face_landmarks, frame_shape: Tuple[int, int, int]) -> Optional[np.ndarray]:
        """
        Extract lip landmark coordinates.
        
        Args:
            face_landmarks: MediaPipe face landmarks
            frame_shape: Shape of the input frame (H, W, C)
            
        Returns:
            Lip landmarks array (N, 2) or None if extraction failed
        """
        try:
            h, w = frame_shape[:2]
            landmarks = []
            
            for idx in self.LIP_LANDMARKS:
                landmark = face_landmarks.landmark[idx]
                x = int(landmark.x * w)
                y = int(landmark.y * h)
                landmarks.append([x, y])
            
            return np.array(landmarks, dtype=np.float32)
            
        except Exception as e:
            logger.error(f"Error extracting lip landmarks: {e}")
            return None
    
    def _compute_features(self, landmarks: np.ndarray) -> np.ndarray:
        """
        Compute feature vector from lip landmarks.
        
        Args:
            landmarks: Lip landmarks array (N, 2)
            
        Returns:
            Feature vector
        """
        features = []
        
        # 1. Raw landmark coordinates (normalized)
        normalized_landmarks = self._normalize_landmarks(landmarks)
        features.extend(normalized_landmarks.flatten())
        
        # 2. Geometric features
        geometric_features = self._compute_geometric_features(landmarks)
        features.extend(geometric_features)
        
        # 3. Temporal features (if previous frame available)
        if self.previous_landmarks is not None:
            temporal_features = self._compute_temporal_features(landmarks, self.previous_landmarks)
            features.extend(temporal_features)
        else:
            # Pad with zeros if no previous frame
            features.extend([0.0] * 10)  # Assuming 10 temporal features
        
        return np.array(features, dtype=np.float32)
    
    def _normalize_landmarks(self, landmarks: np.ndarray) -> np.ndarray:
        """
        Normalize landmarks relative to face center and scale.
        
        Args:
            landmarks: Raw landmark coordinates
            
        Returns:
            Normalized landmarks
        """
        # Center landmarks around mouth center
        mouth_center = np.mean(landmarks, axis=0)
        centered = landmarks - mouth_center
        
        # Scale by mouth width for size invariance
        mouth_width = np.max(landmarks[:, 0]) - np.min(landmarks[:, 0])
        if mouth_width > 0:
            scaled = centered / mouth_width
        else:
            scaled = centered
        
        return scaled
    
    def _compute_geometric_features(self, landmarks: np.ndarray) -> List[float]:
        """
        Compute geometric features from landmarks.
        
        Args:
            landmarks: Lip landmarks
            
        Returns:
            List of geometric features
        """
        features = []
        
        # Mouth width and height
        mouth_width = np.max(landmarks[:, 0]) - np.min(landmarks[:, 0])
        mouth_height = np.max(landmarks[:, 1]) - np.min(landmarks[:, 1])
        features.extend([mouth_width, mouth_height])
        
        # Aspect ratio
        aspect_ratio = mouth_height / max(mouth_width, 1e-6)
        features.append(aspect_ratio)
        
        # Lip distances
        upper_lip_points = landmarks[:len(self.UPPER_LIP)]
        lower_lip_points = landmarks[len(self.UPPER_LIP):len(self.UPPER_LIP)+len(self.LOWER_LIP)]
        
        # Average distance between upper and lower lip
        if len(upper_lip_points) > 0 and len(lower_lip_points) > 0:
            lip_distance = np.mean([
                np.linalg.norm(up - lp) 
                for up in upper_lip_points 
                for lp in lower_lip_points
            ])
            features.append(lip_distance)
        else:
            features.append(0.0)
        
        # Lip corner distance
        if len(landmarks) >= 2:
            corner_distance = np.linalg.norm(landmarks[0] - landmarks[1])
            features.append(corner_distance)
        else:
            features.append(0.0)
        
        return features

    def _compute_temporal_features(self, current_landmarks: np.ndarray, previous_landmarks: np.ndarray) -> List[float]:
        """
        Compute temporal features between consecutive frames.

        Args:
            current_landmarks: Current frame landmarks
            previous_landmarks: Previous frame landmarks

        Returns:
            List of temporal features
        """
        features = []

        # Landmark displacement
        displacement = current_landmarks - previous_landmarks

        # Average displacement magnitude
        avg_displacement = np.mean(np.linalg.norm(displacement, axis=1))
        features.append(avg_displacement)

        # Maximum displacement
        max_displacement = np.max(np.linalg.norm(displacement, axis=1))
        features.append(max_displacement)

        # Displacement variance
        displacement_variance = np.var(np.linalg.norm(displacement, axis=1))
        features.append(displacement_variance)

        # Horizontal and vertical movement
        horizontal_movement = np.mean(np.abs(displacement[:, 0]))
        vertical_movement = np.mean(np.abs(displacement[:, 1]))
        features.extend([horizontal_movement, vertical_movement])

        # Mouth opening change
        current_height = np.max(current_landmarks[:, 1]) - np.min(current_landmarks[:, 1])
        previous_height = np.max(previous_landmarks[:, 1]) - np.min(previous_landmarks[:, 1])
        height_change = current_height - previous_height
        features.append(height_change)

        # Mouth width change
        current_width = np.max(current_landmarks[:, 0]) - np.min(current_landmarks[:, 0])
        previous_width = np.max(previous_landmarks[:, 0]) - np.min(previous_landmarks[:, 0])
        width_change = current_width - previous_width
        features.append(width_change)

        # Lip corner movement
        if len(current_landmarks) >= 2 and len(previous_landmarks) >= 2:
            corner_movement = np.linalg.norm(
                (current_landmarks[0] - current_landmarks[1]) -
                (previous_landmarks[0] - previous_landmarks[1])
            )
            features.append(corner_movement)
        else:
            features.append(0.0)

        # Centroid movement
        current_centroid = np.mean(current_landmarks, axis=0)
        previous_centroid = np.mean(previous_landmarks, axis=0)
        centroid_movement = np.linalg.norm(current_centroid - previous_centroid)
        features.append(centroid_movement)

        # Velocity approximation (assuming constant frame rate)
        velocity = avg_displacement * self.config.sequence_length  # Rough velocity estimate
        features.append(velocity)

        return features

    def _update_history(self, landmarks: np.ndarray) -> None:
        """
        Update landmark history for temporal features.

        Args:
            landmarks: Current landmarks
        """
        self.previous_landmarks = landmarks.copy()

        # Add to history buffer
        self.landmark_history.append(landmarks.copy())

        # Maintain max history size
        if len(self.landmark_history) > self.max_history:
            self.landmark_history.pop(0)

    def get_feature_dimension(self) -> int:
        """
        Get the dimension of the feature vector.

        Returns:
            Feature vector dimension
        """
        # This should match the actual feature computation
        # Raw landmarks: len(LIP_LANDMARKS) * 2 = 24 * 2 = 48
        # Geometric features: 5
        # Temporal features: 10
        return len(self.LIP_LANDMARKS) * 2 + 5 + 10

    def reset_history(self) -> None:
        """Reset landmark history."""
        self.previous_landmarks = None
        self.landmark_history = []

    def get_statistics(self) -> Dict[str, Any]:
        """
        Get extraction statistics.

        Returns:
            Dictionary with statistics
        """
        success_rate = self.successful_extractions / max(1, self.frames_processed)

        return {
            'frames_processed': self.frames_processed,
            'successful_extractions': self.successful_extractions,
            'success_rate': success_rate,
            'feature_dimension': self.get_feature_dimension(),
            'history_length': len(self.landmark_history)
        }

    def reset_statistics(self) -> None:
        """Reset extraction statistics."""
        self.frames_processed = 0
        self.successful_extractions = 0
