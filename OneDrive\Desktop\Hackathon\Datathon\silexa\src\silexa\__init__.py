"""
Silexa - AI-Powered Silent Speech Interpreter

A revolutionary system that converts lip movements into text and speech in real-time
using computer vision and deep learning, designed for accessibility and communication.
"""

__version__ = "0.1.0"
__author__ = "Silexa Development Team"
__email__ = "<EMAIL>"

from .core.interpreter import SilentSpeechInterpreter
from .core.config import SilexaConfig
from .data_collection.video_processor import VideoProcessor
from .feature_extraction.lip_extractor import LipFeatureExtractor
from .models.lip_reader import LipReadingModel
from .speech_synthesis.tts_engine import TTSEngine

__all__ = [
    "SilentSpeechInterpreter",
    "SilexaConfig", 
    "VideoProcessor",
    "LipFeatureExtractor",
    "LipReadingModel",
    "TTSEngine",
]
