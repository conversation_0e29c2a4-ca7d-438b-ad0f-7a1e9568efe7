"""
Basic usage example for Silexa Silent Speech Interpreter
"""

import sys
import os
import time

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from silexa import SilentSpeechInterpreter, SilexaConfig


def main():
    """Demonstrate basic Silexa usage"""
    
    print("Silexa - AI-Powered Silent Speech Interpreter")
    print("=" * 50)
    
    # Create configuration
    config = SilexaConfig()
    print(f"Using device: {config.get_device()}")
    
    # Initialize the interpreter
    print("Initializing Silexa...")
    interpreter = SilentSpeechInterpreter(config)
    
    # Test TTS functionality
    print("\n1. Testing Text-to-Speech...")
    test_text = "Hello, this is Silexa speaking. I can convert text to speech."
    print(f"Speaking: {test_text}")
    
    success = interpreter.synthesize_speech(test_text)
    if success:
        print("✓ TTS test successful")
    else:
        print("✗ TTS test failed")
    
    # Test model building (without training)
    print("\n2. Testing Model Architecture...")
    try:
        model = interpreter.lip_reader.build_model(vocab_size=1000)
        print(f"✓ Model built successfully with {model.count_params()} parameters")
        print(f"Model type: {config.model.model_type}")
        print(f"Input shape: {model.input_shape}")
        print(f"Output shape: {model.output_shape}")
    except Exception as e:
        print(f"✗ Model building failed: {e}")
    
    # Test video processor initialization
    print("\n3. Testing Video Processor...")
    try:
        stats = interpreter.video_processor.get_statistics()
        print(f"✓ Video processor initialized")
        print(f"Buffer size: {stats['buffer_size']}")
        print(f"Is capturing: {stats['is_capturing']}")
    except Exception as e:
        print(f"✗ Video processor test failed: {e}")
    
    # Test feature extractor
    print("\n4. Testing Feature Extractor...")
    try:
        feature_dim = interpreter.lip_extractor.get_feature_dimension()
        stats = interpreter.lip_extractor.get_statistics()
        print(f"✓ Feature extractor initialized")
        print(f"Feature dimension: {feature_dim}")
        print(f"Success rate: {stats['success_rate']:.2f}")
    except Exception as e:
        print(f"✗ Feature extractor test failed: {e}")
    
    # Test configuration
    print("\n5. Testing Configuration...")
    try:
        # Save and load config
        config_path = "test_config.yaml"
        config.to_yaml(config_path)
        loaded_config = SilexaConfig.from_yaml(config_path)
        
        print(f"✓ Configuration save/load successful")
        print(f"Video resolution: {loaded_config.video.input_width}x{loaded_config.video.input_height}")
        print(f"Model type: {loaded_config.model.model_type}")
        print(f"TTS language: {loaded_config.tts.language}")
        
        # Cleanup
        os.remove(config_path)
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
    
    print("\n" + "=" * 50)
    print("Basic tests completed!")
    print("\nTo start live lip reading:")
    print("  python -m silexa.cli live --camera 0")
    print("\nTo process a video file:")
    print("  python -m silexa.cli process video.mp4")
    print("\nTo train a model:")
    print("  python -m silexa.cli train --dataset-path data/ --model-output model.h5")


if __name__ == "__main__":
    main()
