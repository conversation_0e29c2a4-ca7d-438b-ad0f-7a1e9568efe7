Collecting tensorflow
  Downloading tensorflow-2.19.0-cp311-cp311-win_amd64.whl.metadata (4.1 kB)
Requirement already satisfied: absl-py>=1.0.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from tensorflow) (2.3.1)
Collecting astunparse>=1.6.0 (from tensorflow)
  Downloading astunparse-1.6.3-py2.py3-none-any.whl.metadata (4.4 kB)
Requirement already satisfied: flatbuffers>=24.3.25 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from tensorflow) (25.2.10)
Collecting gast!=0.5.0,!=0.5.1,!=0.5.2,>=0.2.1 (from tensorflow)
  Downloading gast-0.6.0-py3-none-any.whl.metadata (1.3 kB)
Collecting google-pasta>=0.1.1 (from tensorflow)
  Downloading google_pasta-0.2.0-py3-none-any.whl.metadata (814 bytes)
Collecting libclang>=13.0.0 (from tensorflow)
  Downloading libclang-18.1.1-py2.py3-none-win_amd64.whl.metadata (5.3 kB)
Requirement already satisfied: opt-einsum>=2.3.2 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from tensorflow) (3.4.0)
Requirement already satisfied: packaging in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from tensorflow) (25.0)
Requirement already satisfied: protobuf!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<6.0.0dev,>=3.20.3 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from tensorflow) (4.25.8)
Requirement already satisfied: requests<3,>=2.21.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from tensorflow) (2.31.0)
Requirement already satisfied: setuptools in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from tensorflow) (65.5.0)
Requirement already satisfied: six>=1.12.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from tensorflow) (1.17.0)
Collecting termcolor>=1.1.0 (from tensorflow)
  Downloading termcolor-3.1.0-py3-none-any.whl.metadata (6.4 kB)
Requirement already satisfied: typing-extensions>=3.6.6 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from tensorflow) (4.13.2)
Collecting wrapt>=1.11.0 (from tensorflow)
  Downloading wrapt-1.17.2-cp311-cp311-win_amd64.whl.metadata (6.5 kB)
Collecting grpcio<2.0,>=1.24.3 (from tensorflow)
  Downloading grpcio-1.74.0-cp311-cp311-win_amd64.whl.metadata (4.0 kB)
Collecting tensorboard~=2.19.0 (from tensorflow)
  Downloading tensorboard-2.19.0-py3-none-any.whl.metadata (1.8 kB)
Collecting keras>=3.5.0 (from tensorflow)
  Downloading keras-3.11.1-py3-none-any.whl.metadata (5.9 kB)
Requirement already satisfied: numpy<2.2.0,>=1.26.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from tensorflow) (1.26.4)
Collecting h5py>=3.11.0 (from tensorflow)
  Downloading h5py-3.14.0-cp311-cp311-win_amd64.whl.metadata (2.7 kB)
Requirement already satisfied: ml-dtypes<1.0.0,>=0.5.1 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from tensorflow) (0.5.3)
Collecting tensorflow-io-gcs-filesystem>=0.23.1 (from tensorflow)
  Downloading tensorflow_io_gcs_filesystem-0.31.0-cp311-cp311-win_amd64.whl.metadata (14 kB)
Collecting wheel<1.0,>=0.23.0 (from astunparse>=1.6.0->tensorflow)
  Downloading wheel-0.45.1-py3-none-any.whl.metadata (2.3 kB)
Collecting rich (from keras>=3.5.0->tensorflow)
  Downloading rich-14.1.0-py3-none-any.whl.metadata (18 kB)
Collecting namex (from keras>=3.5.0->tensorflow)
  Downloading namex-0.1.0-py3-none-any.whl.metadata (322 bytes)
Collecting optree (from keras>=3.5.0->tensorflow)
  Downloading optree-0.17.0-cp311-cp311-win_amd64.whl.metadata (34 kB)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from requests<3,>=2.21.0->tensorflow) (3.4.2)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from requests<3,>=2.21.0->tensorflow) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from requests<3,>=2.21.0->tensorflow) (2.4.0)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from requests<3,>=2.21.0->tensorflow) (2025.4.26)
Collecting markdown>=2.6.8 (from tensorboard~=2.19.0->tensorflow)
  Downloading markdown-3.8.2-py3-none-any.whl.metadata (5.1 kB)
Collecting tensorboard-data-server<0.8.0,>=0.7.0 (from tensorboard~=2.19.0->tensorflow)
  Downloading tensorboard_data_server-0.7.2-py3-none-any.whl.metadata (1.1 kB)
Collecting werkzeug>=1.0.1 (from tensorboard~=2.19.0->tensorflow)
  Downloading werkzeug-3.1.3-py3-none-any.whl.metadata (3.7 kB)
Requirement already satisfied: MarkupSafe>=2.1.1 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from werkzeug>=1.0.1->tensorboard~=2.19.0->tensorflow) (3.0.2)
Collecting markdown-it-py>=2.2.0 (from rich->keras>=3.5.0->tensorflow)
  Using cached markdown_it_py-3.0.0-py3-none-any.whl.metadata (6.9 kB)
Collecting pygments<3.0.0,>=2.13.0 (from rich->keras>=3.5.0->tensorflow)
  Downloading pygments-2.19.2-py3-none-any.whl.metadata (2.5 kB)
Collecting mdurl~=0.1 (from markdown-it-py>=2.2.0->rich->keras>=3.5.0->tensorflow)
  Using cached mdurl-0.1.2-py3-none-any.whl.metadata (1.6 kB)
Downloading tensorflow-2.19.0-cp311-cp311-win_amd64.whl (375.9 MB)
   ---------------------------------------- 375.9/375.9 MB 1.3 MB/s eta 0:00:00
Downloading astunparse-1.6.3-py2.py3-none-any.whl (12 kB)
Downloading gast-0.6.0-py3-none-any.whl (21 kB)
Downloading google_pasta-0.2.0-py3-none-any.whl (57 kB)
   ---------------------------------------- 57.5/57.5 kB 1.5 MB/s eta 0:00:00
Downloading grpcio-1.74.0-cp311-cp311-win_amd64.whl (4.5 MB)
   ---------------------------------------- 4.5/4.5 MB 4.9 MB/s eta 0:00:00
Downloading h5py-3.14.0-cp311-cp311-win_amd64.whl (2.9 MB)
   ---------------------------------------- 2.9/2.9 MB 4.8 MB/s eta 0:00:00
Downloading keras-3.11.1-py3-none-any.whl (1.4 MB)
   ---------------------------------------- 1.4/1.4 MB 5.3 MB/s eta 0:00:00
Downloading libclang-18.1.1-py2.py3-none-win_amd64.whl (26.4 MB)
   ---------------------------------------- 26.4/26.4 MB 3.5 MB/s eta 0:00:00
Downloading tensorboard-2.19.0-py3-none-any.whl (5.5 MB)
   ---------------------------------------- 5.5/5.5 MB 3.7 MB/s eta 0:00:00
Downloading tensorflow_io_gcs_filesystem-0.31.0-cp311-cp311-win_amd64.whl (1.5 MB)
   ---------------------------------------- 1.5/1.5 MB 4.1 MB/s eta 0:00:00
Downloading termcolor-3.1.0-py3-none-any.whl (7.7 kB)
Downloading wrapt-1.17.2-cp311-cp311-win_amd64.whl (38 kB)
Downloading markdown-3.8.2-py3-none-any.whl (106 kB)
   ---------------------------------------- 106.8/106.8 kB 3.1 MB/s eta 0:00:00
Downloading tensorboard_data_server-0.7.2-py3-none-any.whl (2.4 kB)
Downloading werkzeug-3.1.3-py3-none-any.whl (224 kB)
   ---------------------------------------- 224.5/224.5 kB 4.6 MB/s eta 0:00:00
Downloading wheel-0.45.1-py3-none-any.whl (72 kB)
   ---------------------------------------- 72.5/72.5 kB 3.9 MB/s eta 0:00:00
Downloading namex-0.1.0-py3-none-any.whl (5.9 kB)
Downloading optree-0.17.0-cp311-cp311-win_amd64.whl (313 kB)
   ---------------------------------------- 313.8/313.8 kB 4.8 MB/s eta 0:00:00
Downloading rich-14.1.0-py3-none-any.whl (243 kB)
   ---------------------------------------- 243.4/243.4 kB 3.8 MB/s eta 0:00:00
Using cached markdown_it_py-3.0.0-py3-none-any.whl (87 kB)
Downloading pygments-2.19.2-py3-none-any.whl (1.2 MB)
   ---------------------------------------- 1.2/1.2 MB 4.1 MB/s eta 0:00:00
Using cached mdurl-0.1.2-py3-none-any.whl (10.0 kB)
Installing collected packages: namex, libclang, wrapt, wheel, werkzeug, termcolor, tensorflow-io-gcs-filesystem, tensorboard-data-server, pygments, optree, mdurl, markdown, h5py, grpcio, google-pasta, gast, tensorboard, markdown-it-py, astunparse, rich, keras, tensorflow
Successfully installed astunparse-1.6.3 gast-0.6.0 google-pasta-0.2.0 grpcio-1.74.0 h5py-3.14.0 keras-3.11.1 libclang-18.1.1 markdown-3.8.2 markdown-it-py-3.0.0 mdurl-0.1.2 namex-0.1.0 optree-0.17.0 pygments-2.19.2 rich-14.1.0 tensorboard-2.19.0 tensorboard-data-server-0.7.2 tensorflow-2.19.0 tensorflow-io-gcs-filesystem-0.31.0 termcolor-3.1.0 werkzeug-3.1.3 wheel-0.45.1 wrapt-1.17.2
