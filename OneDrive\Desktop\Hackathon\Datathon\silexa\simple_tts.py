"""
Simple Text-to-Speech that actually works on Windows
"""

import sys
import os

def speak_text(text, method="auto"):
    """
    Speak text using available TTS methods
    
    Args:
        text: Text to speak
        method: "auto", "windows", "espeak", "say", or "print"
    """
    
    if not text or not text.strip():
        print("❌ No text to speak!")
        return False
    
    print(f"🔊 Speaking: '{text}'")
    
    # Try different TTS methods
    if method == "auto":
        # Try Windows SAPI first
        if sys.platform == "win32":
            if speak_windows_sapi(text):
                return True
        
        # Try system commands
        if speak_system_command(text):
            return True
        
        # Fallback to print
        speak_print_fallback(text)
        return True
    
    elif method == "windows":
        return speak_windows_sapi(text)
    
    elif method == "print":
        speak_print_fallback(text)
        return True
    
    else:
        print(f"❌ Unknown TTS method: {method}")
        return False


def speak_windows_sapi(text):
    """Use Windows Speech API (SAPI)"""
    try:
        import win32com.client
        
        # Create SAPI voice object
        speaker = win32com.client.Dispatch("SAPI.SpVoice")
        
        # Speak the text
        speaker.Speak(text)
        
        print("✅ Windows SAPI speech successful")
        return True
        
    except ImportError:
        print("⚠️ Windows SAPI not available (pywin32 not installed)")
        return False
    except Exception as e:
        print(f"⚠️ Windows SAPI error: {e}")
        return False


def speak_system_command(text):
    """Use system TTS commands"""
    try:
        if sys.platform == "win32":
            # Windows PowerShell TTS
            import subprocess
            
            # Escape quotes in text
            escaped_text = text.replace('"', '""')
            
            # PowerShell command for TTS
            cmd = f'powershell -Command "Add-Type -AssemblyName System.Speech; $synth = New-Object System.Speech.Synthesis.SpeechSynthesizer; $synth.Speak(\'{escaped_text}\')"'
            
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print("✅ PowerShell TTS successful")
                return True
            else:
                print(f"⚠️ PowerShell TTS failed: {result.stderr}")
                return False
                
        elif sys.platform == "darwin":
            # macOS
            os.system(f'say "{text}"')
            print("✅ macOS say command successful")
            return True
            
        elif sys.platform.startswith("linux"):
            # Linux - try espeak
            if os.system(f'espeak "{text}" 2>/dev/null') == 0:
                print("✅ Linux espeak successful")
                return True
            else:
                print("⚠️ Linux espeak not available")
                return False
        
        return False
        
    except Exception as e:
        print(f"⚠️ System command TTS error: {e}")
        return False


def speak_print_fallback(text):
    """Fallback: Print text with visual indication"""
    print("\n" + "="*50)
    print("🔊 TEXT-TO-SPEECH OUTPUT:")
    print(f"📢 '{text}'")
    print("="*50)
    print("💡 Audio would be played here with proper TTS setup")
    print()


def test_tts():
    """Test different TTS methods"""
    test_text = "Hello! This is Silexa, your AI-powered silent speech interpreter."
    
    print("🧪 Testing Text-to-Speech Methods")
    print("="*40)
    
    methods = ["windows", "auto"]
    
    for method in methods:
        print(f"\n🔧 Testing method: {method}")
        success = speak_text(test_text, method)
        print(f"Result: {'✅ Success' if success else '❌ Failed'}")
    
    print("\n🎉 TTS testing completed!")


def main():
    """Interactive TTS testing"""
    print("🎯 Silexa Text-to-Speech Tester")
    print("="*35)
    
    while True:
        try:
            text = input("\n💬 Enter text to speak (or 'quit' to exit): ").strip()
            
            if text.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if not text:
                print("⚠️ Please enter some text")
                continue
            
            speak_text(text)
            
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")


if __name__ == "__main__":
    # Run tests first
    test_tts()
    
    # Then interactive mode
    main()
