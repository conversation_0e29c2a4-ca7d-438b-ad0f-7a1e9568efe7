"""
Main GUI window for Silexa application
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import cv2
from PIL import Image, ImageTk
import numpy as np
from loguru import logger

from ..core.config import SilexaConfig
from ..core.interpreter import SilentSpeechInterpreter
from ..training.trainer import LipReadingTrainer


class SilexaMainWindow:
    """
    Main GUI application for Silexa Silent Speech Interpreter
    """
    
    def __init__(self):
        """Initialize the main window"""
        self.root = tk.Tk()
        self.root.title("Silexa - AI-Powered Silent Speech Interpreter")
        self.root.geometry("1000x700")
        
        # Configuration and core components
        self.config = SilexaConfig()
        self.interpreter = SilentSpeechInterpreter(self.config)
        self.trainer = LipReadingTrainer(self.config)
        
        # GUI state
        self.is_running = False
        self.current_frame = None
        self.video_thread = None
        
        # Create GUI
        self.create_widgets()
        self.setup_layout()
        
        logger.info("Silexa GUI initialized")
    
    def create_widgets(self):
        """Create all GUI widgets"""
        
        # Main title
        title_frame = ttk.Frame(self.root)
        title_label = ttk.Label(title_frame, text="Silexa - Silent Speech Interpreter", 
                               font=("Arial", 20, "bold"))
        title_label.pack(pady=10)
        
        subtitle_label = ttk.Label(title_frame, text="AI-Powered Lip Reading Technology", 
                                  font=("Arial", 12))
        subtitle_label.pack()
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        
        # Live Recognition Tab
        self.live_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.live_frame, text="Live Recognition")
        self.create_live_tab()
        
        # Training Tab
        self.training_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.training_frame, text="Model Training")
        self.create_training_tab()
        
        # Settings Tab
        self.settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.settings_frame, text="Settings")
        self.create_settings_tab()
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        self.status_bar = ttk.Label(self.root, textvariable=self.status_var, 
                                   relief=tk.SUNKEN, anchor=tk.W)
        
        # Store references
        self.title_frame = title_frame
    
    def create_live_tab(self):
        """Create the live recognition tab"""
        
        # Video display frame
        video_frame = ttk.LabelFrame(self.live_frame, text="Camera Feed")
        video_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")
        
        self.video_label = ttk.Label(video_frame, text="Camera feed will appear here")
        self.video_label.pack(padx=10, pady=10)
        
        # Controls frame
        controls_frame = ttk.LabelFrame(self.live_frame, text="Controls")
        controls_frame.grid(row=0, column=1, padx=10, pady=10, sticky="nsew")
        
        # Start/Stop button
        self.start_button = ttk.Button(controls_frame, text="Start Recognition", 
                                      command=self.toggle_recognition)
        self.start_button.pack(pady=5, fill=tk.X)
        
        # Model selection
        ttk.Label(controls_frame, text="Model:").pack(pady=(10,0))
        self.model_var = tk.StringVar()
        self.model_combo = ttk.Combobox(controls_frame, textvariable=self.model_var, 
                                       state="readonly")
        self.model_combo.pack(pady=5, fill=tk.X)
        self.refresh_models()
        
        # Camera selection
        ttk.Label(controls_frame, text="Camera:").pack(pady=(10,0))
        self.camera_var = tk.StringVar(value="0")
        camera_combo = ttk.Combobox(controls_frame, textvariable=self.camera_var,
                                   values=["0", "1", "2"], state="readonly")
        camera_combo.pack(pady=5, fill=tk.X)
        
        # Results frame
        results_frame = ttk.LabelFrame(self.live_frame, text="Recognition Results")
        results_frame.grid(row=1, column=0, columnspan=2, padx=10, pady=10, sticky="ew")
        
        # Current text
        ttk.Label(results_frame, text="Detected Text:").pack(anchor=tk.W)
        self.text_var = tk.StringVar()
        text_label = ttk.Label(results_frame, textvariable=self.text_var, 
                              font=("Arial", 14, "bold"), foreground="blue")
        text_label.pack(anchor=tk.W, pady=5)
        
        # Confidence
        ttk.Label(results_frame, text="Confidence:").pack(anchor=tk.W)
        self.confidence_var = tk.StringVar()
        confidence_label = ttk.Label(results_frame, textvariable=self.confidence_var)
        confidence_label.pack(anchor=tk.W)
        
        # Speech controls
        speech_frame = ttk.Frame(results_frame)
        speech_frame.pack(fill=tk.X, pady=10)
        
        self.speak_button = ttk.Button(speech_frame, text="Speak Text", 
                                      command=self.speak_current_text)
        self.speak_button.pack(side=tk.LEFT, padx=5)
        
        self.clear_button = ttk.Button(speech_frame, text="Clear", 
                                      command=self.clear_results)
        self.clear_button.pack(side=tk.LEFT, padx=5)
        
        # Configure grid weights
        self.live_frame.grid_columnconfigure(0, weight=2)
        self.live_frame.grid_columnconfigure(1, weight=1)
        self.live_frame.grid_rowconfigure(0, weight=1)
    
    def create_training_tab(self):
        """Create the model training tab"""
        
        # Training controls
        controls_frame = ttk.LabelFrame(self.training_frame, text="Training Controls")
        controls_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Quick training button
        quick_train_button = ttk.Button(controls_frame, text="Quick Train (Demo)", 
                                       command=self.quick_train_model)
        quick_train_button.pack(pady=5, fill=tk.X)
        
        # Training parameters
        params_frame = ttk.Frame(controls_frame)
        params_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(params_frame, text="Samples:").grid(row=0, column=0, sticky=tk.W)
        self.samples_var = tk.StringVar(value="1000")
        samples_entry = ttk.Entry(params_frame, textvariable=self.samples_var, width=10)
        samples_entry.grid(row=0, column=1, padx=5)
        
        ttk.Label(params_frame, text="Epochs:").grid(row=0, column=2, sticky=tk.W, padx=(20,0))
        self.epochs_var = tk.StringVar(value="20")
        epochs_entry = ttk.Entry(params_frame, textvariable=self.epochs_var, width=10)
        epochs_entry.grid(row=0, column=3, padx=5)
        
        # Progress frame
        progress_frame = ttk.LabelFrame(self.training_frame, text="Training Progress")
        progress_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.progress_var = tk.StringVar()
        progress_label = ttk.Label(progress_frame, textvariable=self.progress_var)
        progress_label.pack(pady=5)
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, pady=5)
        
        # Results frame
        results_frame = ttk.LabelFrame(self.training_frame, text="Training Results")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.training_results = tk.Text(results_frame, height=15, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.training_results.yview)
        self.training_results.configure(yscrollcommand=scrollbar.set)
        
        self.training_results.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_settings_tab(self):
        """Create the settings tab"""
        
        # Model settings
        model_frame = ttk.LabelFrame(self.settings_frame, text="Model Settings")
        model_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(model_frame, text="Model Type:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.model_type_var = tk.StringVar(value=self.config.model.model_type)
        model_type_combo = ttk.Combobox(model_frame, textvariable=self.model_type_var,
                                       values=["lstm", "transformer", "cnn_lstm"], state="readonly")
        model_type_combo.grid(row=0, column=1, sticky=tk.W, padx=10, pady=5)
        
        # TTS settings
        tts_frame = ttk.LabelFrame(self.settings_frame, text="Text-to-Speech Settings")
        tts_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(tts_frame, text="Language:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.language_var = tk.StringVar(value=self.config.tts.language)
        language_combo = ttk.Combobox(tts_frame, textvariable=self.language_var,
                                     values=["en", "es", "fr", "de", "it"], state="readonly")
        language_combo.grid(row=0, column=1, sticky=tk.W, padx=10, pady=5)
        
        # Video settings
        video_frame = ttk.LabelFrame(self.settings_frame, text="Video Settings")
        video_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(video_frame, text="Resolution:").grid(row=0, column=0, sticky=tk.W, pady=5)
        resolution_text = f"{self.config.video.input_width}x{self.config.video.input_height}"
        ttk.Label(video_frame, text=resolution_text).grid(row=0, column=1, sticky=tk.W, padx=10, pady=5)
        
        # Apply settings button
        apply_button = ttk.Button(self.settings_frame, text="Apply Settings", 
                                 command=self.apply_settings)
        apply_button.pack(pady=20)
    
    def setup_layout(self):
        """Setup the main layout"""
        self.title_frame.pack(fill=tk.X)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
    
    def refresh_models(self):
        """Refresh the list of available models"""
        try:
            models = self.trainer.get_available_models()
            self.model_combo['values'] = models
            if models:
                self.model_var.set(models[0])
        except Exception as e:
            logger.error(f"Error refreshing models: {e}")
    
    def toggle_recognition(self):
        """Toggle live recognition on/off"""
        if not self.is_running:
            self.start_recognition()
        else:
            self.stop_recognition()
    
    def start_recognition(self):
        """Start live recognition"""
        try:
            camera_id = int(self.camera_var.get())
            
            # Load model if selected
            model_name = self.model_var.get()
            if model_name:
                model_path = self.config.data.models_dir + f"/{model_name}.h5"
                if not self.interpreter.load_model(model_path):
                    messagebox.showerror("Error", f"Failed to load model: {model_name}")
                    return
            
            # Start video capture
            if self.interpreter.start_real_time_mode(camera_id):
                self.is_running = True
                self.start_button.config(text="Stop Recognition")
                self.status_var.set("Recognition running...")
                
                # Start video display thread
                self.video_thread = threading.Thread(target=self.video_loop, daemon=True)
                self.video_thread.start()
            else:
                messagebox.showerror("Error", "Failed to start camera")
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start recognition: {e}")
            logger.error(f"Recognition start error: {e}")
    
    def stop_recognition(self):
        """Stop live recognition"""
        self.is_running = False
        self.interpreter.stop_real_time_mode()
        self.start_button.config(text="Start Recognition")
        self.status_var.set("Recognition stopped")
    
    def video_loop(self):
        """Main video processing loop"""
        while self.is_running:
            try:
                # Get current result
                result = self.interpreter.get_current_result()
                
                # Update GUI in main thread
                self.root.after(0, self.update_results, result)
                
                time.sleep(0.1)  # Update at 10 FPS
                
            except Exception as e:
                logger.error(f"Video loop error: {e}")
                break
    
    def update_results(self, result):
        """Update recognition results in GUI"""
        if result['text']:
            self.text_var.set(result['text'])
            self.confidence_var.set(f"{result['confidence']:.2f}")
        else:
            self.text_var.set("Listening...")
            self.confidence_var.set("0.00")
    
    def speak_current_text(self):
        """Speak the current recognized text"""
        text = self.text_var.get()
        if text and text != "Listening...":
            language = self.language_var.get()
            self.interpreter.synthesize_speech(text, language)
    
    def clear_results(self):
        """Clear recognition results"""
        self.text_var.set("")
        self.confidence_var.set("")
    
    def quick_train_model(self):
        """Start quick model training"""
        try:
            samples = int(self.samples_var.get())
            epochs = int(self.epochs_var.get())
            
            # Start training in separate thread
            training_thread = threading.Thread(
                target=self.run_training, 
                args=(samples, epochs), 
                daemon=True
            )
            training_thread.start()
            
        except ValueError:
            messagebox.showerror("Error", "Please enter valid numbers for samples and epochs")
    
    def run_training(self, samples, epochs):
        """Run training in background thread"""
        try:
            # Update GUI
            self.root.after(0, self.start_training_ui)
            
            # Run training
            model_path = self.trainer.quick_train(samples, epochs)
            
            # Update GUI with results
            self.root.after(0, self.finish_training_ui, model_path)
            
        except Exception as e:
            self.root.after(0, self.training_error, str(e))
    
    def start_training_ui(self):
        """Update UI when training starts"""
        self.progress_var.set("Training in progress...")
        self.progress_bar.start()
        self.training_results.delete(1.0, tk.END)
        self.training_results.insert(tk.END, "Starting model training...\n")
    
    def finish_training_ui(self, model_path):
        """Update UI when training finishes"""
        self.progress_bar.stop()
        self.progress_var.set("Training completed!")
        self.training_results.insert(tk.END, f"Training completed successfully!\n")
        self.training_results.insert(tk.END, f"Model saved to: {model_path}\n")
        self.refresh_models()
    
    def training_error(self, error_msg):
        """Handle training errors"""
        self.progress_bar.stop()
        self.progress_var.set("Training failed!")
        self.training_results.insert(tk.END, f"Training failed: {error_msg}\n")
    
    def apply_settings(self):
        """Apply configuration settings"""
        self.config.model.model_type = self.model_type_var.get()
        self.config.tts.language = self.language_var.get()
        messagebox.showinfo("Settings", "Settings applied successfully!")
    
    def run(self):
        """Start the GUI application"""
        self.root.mainloop()
    
    def on_closing(self):
        """Handle window closing"""
        if self.is_running:
            self.stop_recognition()
        self.root.destroy()
