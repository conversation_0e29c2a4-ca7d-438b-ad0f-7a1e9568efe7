"""
Basic tests for Silexa components
"""

import unittest
import sys
import os
import numpy as np

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from silexa.core.config import SilexaConfig
from silexa.data_collection.video_processor import VideoProcessor
from silexa.feature_extraction.lip_extractor import LipFeatureExtractor
from silexa.models.lip_reader import LipReadingModel
from silexa.speech_synthesis.tts_engine import TTSEngine


class TestSilexaConfig(unittest.TestCase):
    """Test configuration management"""
    
    def test_default_config(self):
        """Test default configuration creation"""
        config = SilexaConfig()
        
        self.assertIsNotNone(config.video)
        self.assertIsNotNone(config.model)
        self.assertIsNotNone(config.tts)
        self.assertIsNotNone(config.data)
        self.assertIsNotNone(config.ui)
        
        # Test default values
        self.assertEqual(config.video.input_width, 640)
        self.assertEqual(config.video.input_height, 480)
        self.assertEqual(config.model.model_type, "lstm")
        self.assertEqual(config.tts.language, "en")
    
    def test_device_detection(self):
        """Test device detection"""
        config = SilexaConfig()
        device = config.get_device()
        
        self.assertIn(device, ["cpu", "gpu"])


class TestVideoProcessor(unittest.TestCase):
    """Test video processing functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        config = SilexaConfig()
        self.video_processor = VideoProcessor(config.video)
    
    def test_initialization(self):
        """Test video processor initialization"""
        self.assertIsNotNone(self.video_processor.face_cascade)
        self.assertFalse(self.video_processor.is_capturing)
    
    def test_frame_preprocessing(self):
        """Test frame preprocessing"""
        # Create a dummy frame
        frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        processed = self.video_processor.preprocess_frame(frame)
        
        self.assertIsNotNone(processed)
        self.assertEqual(processed.shape, (480, 640, 3))
        self.assertEqual(processed.dtype, np.float32)
        self.assertTrue(np.all(processed >= 0) and np.all(processed <= 1))
    
    def test_statistics(self):
        """Test statistics tracking"""
        stats = self.video_processor.get_statistics()
        
        self.assertIn('frames_processed', stats)
        self.assertIn('faces_detected', stats)
        self.assertIn('is_capturing', stats)
        self.assertIn('buffer_size', stats)


class TestLipFeatureExtractor(unittest.TestCase):
    """Test lip feature extraction"""
    
    def setUp(self):
        """Set up test fixtures"""
        config = SilexaConfig()
        self.lip_extractor = LipFeatureExtractor(config.model)
    
    def test_initialization(self):
        """Test feature extractor initialization"""
        self.assertIsNotNone(self.lip_extractor.face_mesh)
        self.assertEqual(len(self.lip_extractor.LIP_LANDMARKS), 24)
    
    def test_feature_dimension(self):
        """Test feature dimension calculation"""
        feature_dim = self.lip_extractor.get_feature_dimension()
        
        # Should be: landmarks (24*2) + geometric (5) + temporal (10) = 63
        self.assertEqual(feature_dim, 63)
    
    def test_statistics(self):
        """Test statistics tracking"""
        stats = self.lip_extractor.get_statistics()
        
        self.assertIn('frames_processed', stats)
        self.assertIn('successful_extractions', stats)
        self.assertIn('success_rate', stats)
        self.assertIn('feature_dimension', stats)


class TestLipReadingModel(unittest.TestCase):
    """Test lip reading model"""
    
    def setUp(self):
        """Set up test fixtures"""
        config = SilexaConfig()
        self.lip_reader = LipReadingModel(config)
    
    def test_initialization(self):
        """Test model initialization"""
        self.assertEqual(self.lip_reader.config.model_type, "lstm")
        self.assertEqual(self.lip_reader.vocab_size, 1000)
        self.assertIsNone(self.lip_reader.model)
    
    def test_model_building(self):
        """Test model building"""
        model = self.lip_reader.build_model(vocab_size=100)
        
        self.assertIsNotNone(model)
        self.assertEqual(model.output_shape[-1], 100)  # vocab_size
        self.assertGreater(model.count_params(), 0)
    
    def test_different_architectures(self):
        """Test different model architectures"""
        architectures = ["lstm", "transformer", "cnn_lstm"]
        
        for arch in architectures:
            config = SilexaConfig()
            config.model.model_type = arch
            
            lip_reader = LipReadingModel(config)
            model = lip_reader.build_model(vocab_size=50)
            
            self.assertIsNotNone(model)
            self.assertEqual(model.output_shape[-1], 50)


class TestTTSEngine(unittest.TestCase):
    """Test text-to-speech engine"""
    
    def setUp(self):
        """Set up test fixtures"""
        config = SilexaConfig()
        self.tts_engine = TTSEngine(config.tts)
    
    def test_initialization(self):
        """Test TTS engine initialization"""
        self.assertIsNotNone(self.tts_engine.config)
        self.assertIsNotNone(self.tts_engine.speech_queue)
        self.assertFalse(self.tts_engine.is_speaking)
    
    def test_emotion_detection(self):
        """Test emotion detection"""
        # Test happy emotion
        emotion = self.tts_engine._detect_emotion("This is great news!")
        self.assertEqual(emotion, "happy")
        
        # Test sad emotion
        emotion = self.tts_engine._detect_emotion("This is terrible")
        self.assertEqual(emotion, "sad")
        
        # Test neutral
        emotion = self.tts_engine._detect_emotion("The weather is normal")
        self.assertEqual(emotion, "neutral")
    
    def test_language_support(self):
        """Test language support"""
        languages = self.tts_engine.get_supported_languages()
        
        self.assertIn("en", languages)
        self.assertTrue(self.tts_engine.is_language_supported("en"))
        self.assertFalse(self.tts_engine.is_language_supported("xyz"))
    
    def test_status(self):
        """Test status reporting"""
        status = self.tts_engine.get_status()
        
        self.assertIn('is_speaking', status)
        self.assertIn('queue_size', status)
        self.assertIn('current_language', status)
        self.assertIn('voice_speed', status)


class TestIntegration(unittest.TestCase):
    """Integration tests"""
    
    def test_full_pipeline_initialization(self):
        """Test that all components can be initialized together"""
        from silexa import SilentSpeechInterpreter
        
        config = SilexaConfig()
        interpreter = SilentSpeechInterpreter(config)
        
        self.assertIsNotNone(interpreter.video_processor)
        self.assertIsNotNone(interpreter.lip_extractor)
        self.assertIsNotNone(interpreter.lip_reader)
        self.assertIsNotNone(interpreter.tts_engine)
    
    def test_feature_extraction_pipeline(self):
        """Test feature extraction with dummy data"""
        config = SilexaConfig()
        lip_extractor = LipFeatureExtractor(config.model)
        
        # Create dummy frame (this won't extract real features without a face)
        dummy_frame = np.random.rand(480, 640, 3).astype(np.float32)
        
        # This should return None since there's no face, but shouldn't crash
        features = lip_extractor.extract_features(dummy_frame)
        
        # Should be None for random noise, but the function should work
        self.assertIsNone(features)


if __name__ == '__main__':
    # Run tests
    unittest.main(verbosity=2)
