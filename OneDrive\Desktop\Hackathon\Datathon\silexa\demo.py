"""
Silexa Demo Script - Showcase the capabilities of the Silent Speech Interpreter
"""

import sys
import os
import time
import argparse

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from silexa import SilentSpeechInterpreter, SilexaConfig


def print_banner():
    """Print the Silexa banner"""
    banner = """
    ███████╗██╗██╗     ███████╗██╗  ██╗ █████╗ 
    ██╔════╝██║██║     ██╔════╝╚██╗██╔╝██╔══██╗
    ███████╗██║██║     █████╗   ╚███╔╝ ███████║
    ╚════██║██║██║     ██╔══╝   ██╔██╗ ██╔══██║
    ███████║██║███████╗███████╗██╔╝ ██╗██║  ██║
    ╚══════╝╚═╝╚══════╝╚══════╝╚═╝  ╚═╝╚═╝  ╚═╝
    
    AI-Powered Silent Speech Interpreter
    Giving Voice to the Voiceless
    """
    print(banner)


def demo_configuration():
    """Demonstrate configuration management"""
    print("\n🔧 Configuration Demo")
    print("-" * 40)
    
    # Create and display configuration
    config = SilexaConfig()
    
    print(f"Device: {config.get_device()}")
    print(f"Video Resolution: {config.video.input_width}x{config.video.input_height}")
    print(f"Model Type: {config.model.model_type}")
    print(f"TTS Language: {config.tts.language}")
    print(f"Feature Dimension: {config.model.feature_dim}")
    
    # Save configuration
    config_path = "demo_config.yaml"
    config.to_yaml(config_path)
    print(f"✓ Configuration saved to {config_path}")
    
    return config


def demo_model_architecture():
    """Demonstrate model building"""
    print("\n🧠 Model Architecture Demo")
    print("-" * 40)
    
    config = SilexaConfig()
    
    # Test different architectures
    architectures = ["lstm", "transformer", "cnn_lstm"]
    
    for arch in architectures:
        print(f"\nBuilding {arch.upper()} model...")
        config.model.model_type = arch
        
        try:
            from silexa.models import LipReadingModel
            model = LipReadingModel(config.model)
            keras_model = model.build_model(vocab_size=1000)
            
            print(f"✓ {arch.upper()} model built successfully")
            print(f"  Parameters: {keras_model.count_params():,}")
            print(f"  Input shape: {keras_model.input_shape}")
            print(f"  Output shape: {keras_model.output_shape}")
            
        except Exception as e:
            print(f"✗ {arch.upper()} model failed: {e}")


def demo_feature_extraction():
    """Demonstrate feature extraction"""
    print("\n👄 Feature Extraction Demo")
    print("-" * 40)
    
    config = SilexaConfig()
    
    try:
        from silexa.feature_extraction import LipFeatureExtractor
        import numpy as np
        
        extractor = LipFeatureExtractor(config.model)
        
        print(f"Feature dimension: {extractor.get_feature_dimension()}")
        print(f"Lip landmarks used: {len(extractor.LIP_LANDMARKS)}")
        print(f"Upper lip points: {len(extractor.UPPER_LIP)}")
        print(f"Lower lip points: {len(extractor.LOWER_LIP)}")
        
        # Test with dummy frame (won't extract real features without face)
        dummy_frame = np.random.rand(480, 640, 3).astype(np.float32)
        features = extractor.extract_features(dummy_frame)
        
        if features is None:
            print("✓ Feature extraction working (no face detected in random data)")
        else:
            print(f"✓ Features extracted: shape {features.shape}")
        
        stats = extractor.get_statistics()
        print(f"Statistics: {stats}")
        
    except Exception as e:
        print(f"✗ Feature extraction demo failed: {e}")


def demo_tts():
    """Demonstrate text-to-speech"""
    print("\n🔊 Text-to-Speech Demo")
    print("-" * 40)
    
    config = SilexaConfig()
    
    try:
        from silexa.speech_synthesis import TTSEngine
        
        tts = TTSEngine(config.tts)
        
        print(f"Supported languages: {tts.get_supported_languages()}")
        print(f"Current language: {config.tts.language}")
        print(f"Emotion detection: {config.tts.emotion_detection}")
        
        # Test emotion detection
        test_texts = [
            "This is wonderful news!",
            "I'm feeling terrible today",
            "That's absolutely amazing!",
            "The weather is normal"
        ]
        
        for text in test_texts:
            emotion = tts._detect_emotion(text)
            print(f"Text: '{text}' → Emotion: {emotion}")
        
        # Test TTS (may not work without audio setup)
        print("\nTesting speech synthesis...")
        demo_text = "Hello, this is Silexa, your AI-powered silent speech interpreter."
        
        success = tts.speak(demo_text)
        if success:
            print("✓ Speech synthesis queued successfully")
            time.sleep(2)  # Give time for speech
        else:
            print("⚠ Speech synthesis queued (audio may not be available)")
        
        status = tts.get_status()
        print(f"TTS Status: {status}")
        
    except Exception as e:
        print(f"✗ TTS demo failed: {e}")


def demo_video_processing():
    """Demonstrate video processing"""
    print("\n📹 Video Processing Demo")
    print("-" * 40)
    
    config = SilexaConfig()
    
    try:
        from silexa.data_collection import VideoProcessor
        import numpy as np
        
        processor = VideoProcessor(config.video)
        
        print(f"Video configuration:")
        print(f"  Resolution: {config.video.input_width}x{config.video.input_height}")
        print(f"  FPS: {config.video.fps}")
        print(f"  Buffer size: {config.video.buffer_size}")
        
        # Test frame preprocessing
        dummy_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        processed = processor.preprocess_frame(dummy_frame)
        
        if processed is not None:
            print(f"✓ Frame preprocessing working")
            print(f"  Input shape: {dummy_frame.shape}, dtype: {dummy_frame.dtype}")
            print(f"  Output shape: {processed.shape}, dtype: {processed.dtype}")
            print(f"  Value range: [{processed.min():.3f}, {processed.max():.3f}]")
        
        stats = processor.get_statistics()
        print(f"Statistics: {stats}")
        
    except Exception as e:
        print(f"✗ Video processing demo failed: {e}")


def demo_full_pipeline():
    """Demonstrate the full pipeline"""
    print("\n🚀 Full Pipeline Demo")
    print("-" * 40)
    
    try:
        config = SilexaConfig()
        interpreter = SilentSpeechInterpreter(config)
        
        print("✓ Silent Speech Interpreter initialized")
        print(f"  Video processor: {type(interpreter.video_processor).__name__}")
        print(f"  Feature extractor: {type(interpreter.lip_extractor).__name__}")
        print(f"  Model: {type(interpreter.lip_reader).__name__}")
        print(f"  TTS engine: {type(interpreter.tts_engine).__name__}")
        
        # Get current result (will be empty without model)
        result = interpreter.get_current_result()
        print(f"\nCurrent result: {result}")
        
        # Test speech synthesis
        test_text = "Silexa is ready to help people with speech impairments communicate naturally."
        print(f"\nTesting speech synthesis with: '{test_text}'")
        
        success = interpreter.synthesize_speech(test_text)
        if success:
            print("✓ Speech synthesis successful")
        else:
            print("⚠ Speech synthesis attempted (audio may not be available)")
        
    except Exception as e:
        print(f"✗ Full pipeline demo failed: {e}")


def demo_cli_commands():
    """Show CLI command examples"""
    print("\n💻 CLI Commands Demo")
    print("-" * 40)
    
    commands = [
        ("silexa info", "Show system information"),
        ("silexa test", "Run system tests"),
        ("silexa live --camera 0", "Start live lip reading"),
        ("silexa process video.mp4", "Process video file"),
        ("silexa speak 'Hello world'", "Text-to-speech"),
        ("silexa init-config", "Create configuration file"),
    ]
    
    print("Available CLI commands:")
    for cmd, desc in commands:
        print(f"  {cmd:<30} - {desc}")
    
    print(f"\nTo run CLI commands:")
    print(f"  python -m silexa.cli <command>")


def main():
    """Main demo function"""
    parser = argparse.ArgumentParser(description="Silexa Demo Script")
    parser.add_argument("--section", choices=[
        "config", "model", "features", "tts", "video", "pipeline", "cli", "all"
    ], default="all", help="Demo section to run")
    
    args = parser.parse_args()
    
    print_banner()
    
    print("🎯 Welcome to the Silexa Demo!")
    print("This demonstration showcases the capabilities of our AI-powered")
    print("Silent Speech Interpreter designed to help people with speech")
    print("impairments communicate naturally through lip reading technology.")
    
    if args.section in ["config", "all"]:
        demo_configuration()
    
    if args.section in ["model", "all"]:
        demo_model_architecture()
    
    if args.section in ["features", "all"]:
        demo_feature_extraction()
    
    if args.section in ["tts", "all"]:
        demo_tts()
    
    if args.section in ["video", "all"]:
        demo_video_processing()
    
    if args.section in ["pipeline", "all"]:
        demo_full_pipeline()
    
    if args.section in ["cli", "all"]:
        demo_cli_commands()
    
    print("\n" + "=" * 60)
    print("🎉 Demo completed!")
    print("\nNext steps:")
    print("1. Install dependencies: python install.py")
    print("2. Run tests: python tests/test_basic.py")
    print("3. Try live mode: python -m silexa.cli live")
    print("4. Read documentation: docs/DEVELOPMENT_GUIDE.md")
    print("\n💡 Silexa - Empowering communication through AI")
    print("   Giving voice to the voiceless with dignity and autonomy")


if __name__ == "__main__":
    main()
