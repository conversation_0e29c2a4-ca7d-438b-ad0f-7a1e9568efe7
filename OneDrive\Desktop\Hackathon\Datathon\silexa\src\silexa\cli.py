"""
Command Line Interface for Silexa
"""

import click
import sys
import os
from pathlib import Path
from loguru import logger

from .core.interpreter import SilentSpeechInterpreter
from .core.config import SilexaConfig


@click.group()
@click.option('--config', '-c', type=click.Path(exists=True), help='Configuration file path')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
@click.pass_context
def cli(ctx, config, verbose):
    """Silexa - AI-Powered Silent Speech Interpreter"""
    
    # Setup logging
    log_level = "DEBUG" if verbose else "INFO"
    logger.remove()
    logger.add(sys.stderr, level=log_level)
    
    # Load configuration
    if config:
        silexa_config = SilexaConfig.from_yaml(config)
    else:
        silexa_config = SilexaConfig()
    
    # Store in context
    ctx.ensure_object(dict)
    ctx.obj['config'] = silexa_config


@cli.command()
@click.option('--camera', '-cam', default=0, help='Camera device ID')
@click.option('--model', '-m', type=click.Path(exists=True), help='Path to trained model')
@click.pass_context
def live(ctx, camera, model):
    """Start live lip reading mode"""
    config = ctx.obj['config']
    
    try:
        # Initialize interpreter
        interpreter = SilentSpeechInterpreter(config)
        
        # Load model if provided
        if model:
            if not interpreter.load_model(model):
                click.echo(f"Failed to load model from {model}", err=True)
                sys.exit(1)
            click.echo(f"Model loaded from {model}")
        else:
            click.echo("Warning: No model loaded. Predictions will not be accurate.")
        
        # Start live mode
        click.echo(f"Starting live lip reading on camera {camera}")
        click.echo("Press Ctrl+C to stop")
        
        if not interpreter.start_real_time_mode(camera):
            click.echo("Failed to start real-time mode", err=True)
            sys.exit(1)
        
        try:
            while True:
                result = interpreter.get_current_result()
                if result['text']:
                    click.echo(f"Detected: {result['text']} (confidence: {result['confidence']:.2f})")
                
                # Small delay to prevent overwhelming output
                import time
                time.sleep(0.5)
                
        except KeyboardInterrupt:
            click.echo("\nStopping live mode...")
            interpreter.stop_real_time_mode()
            
    except Exception as e:
        logger.error(f"Error in live mode: {e}")
        sys.exit(1)


@cli.command()
@click.argument('video_path', type=click.Path(exists=True))
@click.option('--model', '-m', type=click.Path(exists=True), help='Path to trained model')
@click.option('--output', '-o', type=click.Path(), help='Output file for results')
@click.pass_context
def process(ctx, video_path, model, output):
    """Process a video file for lip reading"""
    config = ctx.obj['config']
    
    try:
        # Initialize interpreter
        interpreter = SilentSpeechInterpreter(config)
        
        # Load model if provided
        if model:
            if not interpreter.load_model(model):
                click.echo(f"Failed to load model from {model}", err=True)
                sys.exit(1)
            click.echo(f"Model loaded from {model}")
        
        # Process video
        click.echo(f"Processing video: {video_path}")
        results = interpreter.process_video_file(video_path)
        
        if not results:
            click.echo("No results generated from video", err=True)
            sys.exit(1)
        
        # Display results
        click.echo(f"\nProcessed {len(results)} frames:")
        for result in results:
            click.echo(f"Frame {result['frame_number']:4d} ({result['timestamp']:6.2f}s): "
                      f"{result['text']} (confidence: {result['confidence']:.2f})")
        
        # Save results if output specified
        if output:
            import json
            with open(output, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            click.echo(f"\nResults saved to {output}")
            
    except Exception as e:
        logger.error(f"Error processing video: {e}")
        sys.exit(1)


@cli.command()
@click.argument('text')
@click.option('--language', '-l', default='en', help='Language code for speech synthesis')
@click.pass_context
def speak(ctx, text, language):
    """Convert text to speech"""
    config = ctx.obj['config']
    
    try:
        # Initialize interpreter (for TTS engine)
        interpreter = SilentSpeechInterpreter(config)
        
        # Synthesize speech
        click.echo(f"Speaking: {text}")
        success = interpreter.synthesize_speech(text, language)
        
        if not success:
            click.echo("Failed to synthesize speech", err=True)
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Error in speech synthesis: {e}")
        sys.exit(1)


@cli.command()
@click.option('--dataset-path', '-d', type=click.Path(exists=True), required=True, 
              help='Path to training dataset')
@click.option('--model-output', '-o', type=click.Path(), required=True,
              help='Path to save trained model')
@click.option('--epochs', '-e', default=100, help='Number of training epochs')
@click.option('--batch-size', '-b', default=32, help='Training batch size')
@click.pass_context
def train(ctx, dataset_path, model_output, epochs, batch_size):
    """Train a new lip reading model"""
    config = ctx.obj['config']
    
    # Update training parameters
    config.model.epochs = epochs
    config.model.batch_size = batch_size
    
    try:
        click.echo(f"Training model with dataset: {dataset_path}")
        click.echo(f"Output model will be saved to: {model_output}")
        
        # This would require implementing the training pipeline
        # For now, just show what would happen
        click.echo("Training pipeline not yet implemented in CLI")
        click.echo("Please use the Python API for training")
        
    except Exception as e:
        logger.error(f"Error in training: {e}")
        sys.exit(1)


@cli.command()
@click.option('--output', '-o', type=click.Path(), default='silexa_config.yaml',
              help='Output configuration file path')
@click.pass_context
def init_config(ctx, output):
    """Initialize a new configuration file"""
    try:
        config = SilexaConfig()
        config.to_yaml(output)
        click.echo(f"Configuration file created: {output}")
        
    except Exception as e:
        logger.error(f"Error creating config: {e}")
        sys.exit(1)


@cli.command()
@click.pass_context
def info(ctx):
    """Show system information and configuration"""
    config = ctx.obj['config']
    
    click.echo("Silexa System Information")
    click.echo("=" * 30)
    
    # System info
    import platform
    import tensorflow as tf
    
    click.echo(f"Platform: {platform.system()} {platform.release()}")
    click.echo(f"Python: {platform.python_version()}")
    click.echo(f"TensorFlow: {tf.__version__}")
    
    # GPU info
    gpus = tf.config.list_physical_devices('GPU')
    if gpus:
        click.echo(f"GPUs available: {len(gpus)}")
        for i, gpu in enumerate(gpus):
            click.echo(f"  GPU {i}: {gpu.name}")
    else:
        click.echo("No GPUs available")
    
    # Configuration
    click.echo("\nConfiguration:")
    click.echo(f"  Device: {config.get_device()}")
    click.echo(f"  Model type: {config.model.model_type}")
    click.echo(f"  Video resolution: {config.video.input_width}x{config.video.input_height}")
    click.echo(f"  TTS language: {config.tts.language}")
    click.echo(f"  Data directory: {config.data.data_dir}")


@cli.command()
@click.pass_context
def test(ctx):
    """Run system tests"""
    config = ctx.obj['config']
    
    click.echo("Running Silexa system tests...")
    
    try:
        # Test imports
        click.echo("✓ Testing imports...")
        from .data_collection.video_processor import VideoProcessor
        from .feature_extraction.lip_extractor import LipFeatureExtractor
        from .models.lip_reader import LipReadingModel
        from .speech_synthesis.tts_engine import TTSEngine
        
        # Test video processor
        click.echo("✓ Testing video processor...")
        video_processor = VideoProcessor(config.video)
        
        # Test feature extractor
        click.echo("✓ Testing feature extractor...")
        lip_extractor = LipFeatureExtractor(config.model)
        
        # Test model
        click.echo("✓ Testing model...")
        lip_reader = LipReadingModel(config.model)
        model = lip_reader.build_model(vocab_size=1000)
        
        # Test TTS
        click.echo("✓ Testing TTS engine...")
        tts_engine = TTSEngine(config.tts)
        
        click.echo("\n✅ All tests passed!")
        
    except Exception as e:
        click.echo(f"\n❌ Test failed: {e}", err=True)
        sys.exit(1)


def main():
    """Main entry point"""
    cli()


if __name__ == '__main__':
    main()
