"""
Main Silent Speech Interpreter class
"""

import cv2
import numpy as np
from typing import Optional, List, Dict, Any
import threading
import queue
import time
from loguru import logger

from .config import SilexaConfig, default_config
from ..data_collection.video_processor import VideoProcessor
from ..feature_extraction.lip_extractor import LipFeatureExtractor
from ..models.lip_reader import LipReadingModel
from ..speech_synthesis.tts_engine import TTSEngine


class SilentSpeechInterpreter:
    """
    Main class for the Silent Speech Interpreter system.
    
    This class orchestrates the entire pipeline:
    1. Video capture and processing
    2. Lip feature extraction
    3. Text prediction from lip movements
    4. Text-to-speech synthesis
    """
    
    def __init__(self, config: Optional[SilexaConfig] = None):
        """
        Initialize the Silent Speech Interpreter.
        
        Args:
            config: Configuration object. If None, uses default config.
        """
        self.config = config or default_config
        
        # Initialize components
        self.video_processor = VideoProcessor(self.config.video)
        self.lip_extractor = LipFeatureExtractor(self.config.model)
        self.lip_reader = LipReadingModel(self.config.model)
        self.tts_engine = TTSEngine(self.config.tts)
        
        # State management
        self.is_running = False
        self.is_paused = False
        self.current_text = ""
        self.confidence_score = 0.0
        
        # Threading
        self.processing_thread = None
        self.frame_queue = queue.Queue(maxsize=self.config.video.buffer_size)
        self.result_queue = queue.Queue()
        
        logger.info("Silexa Silent Speech Interpreter initialized")
    
    def load_model(self, model_path: str) -> bool:
        """
        Load a pre-trained lip reading model.
        
        Args:
            model_path: Path to the saved model
            
        Returns:
            True if model loaded successfully, False otherwise
        """
        try:
            success = self.lip_reader.load_model(model_path)
            if success:
                logger.info(f"Model loaded successfully from {model_path}")
            else:
                logger.error(f"Failed to load model from {model_path}")
            return success
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            return False
    
    def start_real_time_mode(self, camera_id: int = 0) -> bool:
        """
        Start real-time lip reading mode.
        
        Args:
            camera_id: Camera device ID (default: 0)
            
        Returns:
            True if started successfully, False otherwise
        """
        if self.is_running:
            logger.warning("Real-time mode is already running")
            return False
        
        try:
            # Initialize video capture
            if not self.video_processor.start_capture(camera_id):
                logger.error("Failed to start video capture")
                return False
            
            self.is_running = True
            self.is_paused = False
            
            # Start processing thread
            self.processing_thread = threading.Thread(
                target=self._processing_loop,
                daemon=True
            )
            self.processing_thread.start()
            
            logger.info("Real-time mode started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error starting real-time mode: {e}")
            return False
    
    def stop_real_time_mode(self) -> None:
        """Stop real-time lip reading mode."""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # Stop video capture
        self.video_processor.stop_capture()
        
        # Wait for processing thread to finish
        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=2.0)
        
        # Clear queues
        self._clear_queues()
        
        logger.info("Real-time mode stopped")
    
    def pause_resume(self) -> None:
        """Toggle pause/resume state."""
        self.is_paused = not self.is_paused
        state = "paused" if self.is_paused else "resumed"
        logger.info(f"Processing {state}")
    
    def process_video_file(self, video_path: str) -> List[Dict[str, Any]]:
        """
        Process a video file and return lip reading results.
        
        Args:
            video_path: Path to the video file
            
        Returns:
            List of results with timestamps, text, and confidence scores
        """
        results = []
        
        try:
            frames = self.video_processor.process_video_file(video_path)
            
            for i, frame in enumerate(frames):
                if frame is None:
                    continue
                
                # Extract lip features
                features = self.lip_extractor.extract_features(frame)
                
                if features is not None:
                    # Predict text
                    text, confidence = self.lip_reader.predict(features)
                    
                    results.append({
                        'timestamp': i / self.config.video.fps,
                        'frame_number': i,
                        'text': text,
                        'confidence': confidence,
                        'features': features
                    })
            
            logger.info(f"Processed {len(results)} frames from {video_path}")
            return results
            
        except Exception as e:
            logger.error(f"Error processing video file: {e}")
            return []
    
    def synthesize_speech(self, text: str, language: str = None) -> bool:
        """
        Convert text to speech.
        
        Args:
            text: Text to synthesize
            language: Language code (if None, uses config default)
            
        Returns:
            True if synthesis successful, False otherwise
        """
        try:
            lang = language or self.config.tts.language
            return self.tts_engine.speak(text, lang)
        except Exception as e:
            logger.error(f"Error in speech synthesis: {e}")
            return False
    
    def get_current_result(self) -> Dict[str, Any]:
        """
        Get the current lip reading result.
        
        Returns:
            Dictionary with current text, confidence, and metadata
        """
        return {
            'text': self.current_text,
            'confidence': self.confidence_score,
            'is_running': self.is_running,
            'is_paused': self.is_paused,
            'timestamp': time.time()
        }
    
    def _processing_loop(self) -> None:
        """Main processing loop for real-time mode."""
        sequence_buffer = []
        
        while self.is_running:
            try:
                if self.is_paused:
                    time.sleep(0.1)
                    continue
                
                # Get frame from video processor
                frame = self.video_processor.get_frame()
                
                if frame is None:
                    time.sleep(0.01)
                    continue
                
                # Extract lip features
                features = self.lip_extractor.extract_features(frame)
                
                if features is not None:
                    # Add to sequence buffer
                    sequence_buffer.append(features)
                    
                    # Maintain sequence length
                    if len(sequence_buffer) > self.config.model.sequence_length:
                        sequence_buffer.pop(0)
                    
                    # Predict if we have enough frames
                    if len(sequence_buffer) == self.config.model.sequence_length:
                        text, confidence = self.lip_reader.predict_sequence(
                            np.array(sequence_buffer)
                        )
                        
                        # Update current result
                        self.current_text = text
                        self.confidence_score = confidence
                        
                        # Add to result queue
                        self.result_queue.put({
                            'text': text,
                            'confidence': confidence,
                            'timestamp': time.time()
                        })
                
            except Exception as e:
                logger.error(f"Error in processing loop: {e}")
                time.sleep(0.1)
    
    def _clear_queues(self) -> None:
        """Clear all queues."""
        while not self.frame_queue.empty():
            try:
                self.frame_queue.get_nowait()
            except queue.Empty:
                break
        
        while not self.result_queue.empty():
            try:
                self.result_queue.get_nowait()
            except queue.Empty:
                break
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.stop_real_time_mode()
