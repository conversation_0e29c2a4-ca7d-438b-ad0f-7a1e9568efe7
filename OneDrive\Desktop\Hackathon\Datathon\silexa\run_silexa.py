"""
Complete Silexa Application Launcher
This script provides a complete working demonstration of Silexa
"""

import sys
import os
import argparse
import time

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def print_banner():
    """Print the Silexa banner"""
    banner = """
    ███████╗██╗██╗     ███████╗██╗  ██╗ █████╗ 
    ██╔════╝██║██║     ██╔════╝╚██╗██╔╝██╔══██╗
    ███████╗██║██║     █████╗   ╚███╔╝ ███████║
    ╚════██║██║██║     ██╔══╝   ██╔██╗ ██╔══██║
    ███████║██║███████╗███████╗██╔╝ ██╗██║  ██║
    ╚══════╝╚═╝╚══════╝╚══════╝╚═╝  ╚═╝╚═╝  ██║
    
    🎯 AI-Powered Silent Speech Interpreter
    💝 Giving Voice to the Voiceless
    """
    print(banner)


def check_dependencies():
    """Check if required dependencies are available"""
    missing_deps = []
    
    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")
    
    try:
        import cv2
    except ImportError:
        missing_deps.append("opencv-python")
    
    try:
        import tensorflow
    except ImportError:
        missing_deps.append("tensorflow")
    
    if missing_deps:
        print("❌ Missing dependencies:")
        for dep in missing_deps:
            print(f"   - {dep}")
        print("\n💡 Install with: pip install " + " ".join(missing_deps))
        return False
    
    print("✅ All core dependencies available")
    return True


def run_complete_demo():
    """Run the complete Silexa demonstration"""
    print("\n🚀 Starting Complete Silexa Demo")
    print("=" * 50)
    
    try:
        # Import Silexa components
        from silexa.core.config import SilexaConfig
        from silexa.training.trainer import LipReadingTrainer
        from silexa.core.interpreter import SilentSpeechInterpreter
        
        print("✅ Silexa modules imported successfully")
        
        # Initialize configuration
        config = SilexaConfig()
        print(f"✅ Configuration initialized (device: {config.get_device()})")
        
        # Initialize trainer
        trainer = LipReadingTrainer(config)
        print("✅ Training system initialized")
        
        # Quick training demonstration
        print("\n🧠 Training AI Model (Quick Demo)")
        print("-" * 30)
        print("Training with synthetic data for demonstration...")
        
        # Train a small model quickly
        model_path = trainer.quick_train(num_samples=500, epochs=10)
        print(f"✅ Model trained and saved to: {model_path}")
        
        # Test the model
        print("\n🔍 Testing Model Predictions")
        print("-" * 30)
        trainer.test_prediction(model_path, num_tests=5)
        
        # Initialize interpreter with trained model
        print("\n🎤 Initializing Speech Interpreter")
        print("-" * 30)
        interpreter = SilentSpeechInterpreter(config)
        
        if interpreter.load_model(model_path):
            print("✅ Model loaded successfully")
        else:
            print("⚠️ Model loading failed, continuing with demo")
        
        # Test TTS
        print("\n🔊 Testing Text-to-Speech")
        print("-" * 30)
        test_text = "Hello! This is Silexa, your AI-powered silent speech interpreter."
        print(f"Speaking: '{test_text}'")
        
        success = interpreter.synthesize_speech(test_text)
        if success:
            print("✅ Speech synthesis successful")
            time.sleep(3)  # Give time for speech
        else:
            print("⚠️ Speech synthesis attempted (audio may not be available)")
        
        print("\n🎉 Complete Demo Finished Successfully!")
        print("\n📋 What was demonstrated:")
        print("  ✅ AI model training with synthetic data")
        print("  ✅ Lip reading prediction system")
        print("  ✅ Text-to-speech synthesis")
        print("  ✅ Complete end-to-end pipeline")
        
        return True
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_gui():
    """Run the GUI application"""
    print("\n🖥️ Starting Silexa GUI Application")
    print("=" * 40)
    
    try:
        # Check for GUI dependencies
        try:
            import tkinter
            from PIL import Image, ImageTk
        except ImportError as e:
            print(f"❌ GUI dependencies missing: {e}")
            print("💡 Install with: pip install pillow")
            return False
        
        # Import and run GUI
        from silexa.ui.main_window import SilexaMainWindow
        
        print("✅ Starting GUI application...")
        app = SilexaMainWindow()
        app.run()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_cli_demo():
    """Run CLI demonstration"""
    print("\n💻 CLI Demonstration")
    print("=" * 30)
    
    try:
        from silexa.core.config import SilexaConfig
        from silexa.core.interpreter import SilentSpeechInterpreter
        
        # Basic functionality test
        config = SilexaConfig()
        interpreter = SilentSpeechInterpreter(config)
        
        print("✅ Core system initialized")
        
        # Show configuration
        print(f"📋 Configuration:")
        print(f"   Device: {config.get_device()}")
        print(f"   Model type: {config.model.model_type}")
        print(f"   Video resolution: {config.video.input_width}x{config.video.input_height}")
        print(f"   TTS language: {config.tts.language}")
        
        # Test components
        print("\n🔧 Component Tests:")
        
        # Video processor
        stats = interpreter.video_processor.get_statistics()
        print(f"   Video processor: ✅ (buffer: {stats['buffer_size']})")
        
        # Feature extractor
        feature_dim = interpreter.lip_extractor.get_feature_dimension()
        print(f"   Feature extractor: ✅ (dim: {feature_dim})")
        
        # Model architecture
        try:
            model = interpreter.lip_reader.build_model(vocab_size=100)
            print(f"   Model architecture: ✅ ({model.count_params():,} params)")
        except Exception as e:
            print(f"   Model architecture: ⚠️ ({e})")
        
        # TTS engine
        tts_status = interpreter.tts_engine.get_status()
        print(f"   TTS engine: ✅ (lang: {tts_status['current_language']})")
        
        print("\n✅ CLI demonstration completed!")
        return True
        
    except Exception as e:
        print(f"❌ CLI demo failed: {e}")
        return False


def run_training_only():
    """Run only the training demonstration"""
    print("\n🧠 Training-Only Demonstration")
    print("=" * 35)
    
    try:
        from silexa.core.config import SilexaConfig
        from silexa.training.trainer import LipReadingTrainer
        
        config = SilexaConfig()
        trainer = LipReadingTrainer(config)
        
        print("🔄 Training AI model with synthetic data...")
        model_path = trainer.quick_train(num_samples=1000, epochs=15)
        
        print(f"✅ Training completed: {model_path}")
        
        # Test predictions
        print("\n🔍 Testing predictions:")
        trainer.test_prediction(model_path, num_tests=10)
        
        return True
        
    except Exception as e:
        print(f"❌ Training failed: {e}")
        return False


def main():
    """Main application entry point"""
    parser = argparse.ArgumentParser(description="Silexa - AI-Powered Silent Speech Interpreter")
    parser.add_argument("--mode", choices=["complete", "gui", "cli", "train"], 
                       default="complete", help="Run mode")
    parser.add_argument("--check-deps", action="store_true", help="Check dependencies only")
    
    args = parser.parse_args()
    
    print_banner()
    
    if args.check_deps:
        check_dependencies()
        return
    
    print("🎯 Welcome to Silexa!")
    print("This demonstration showcases our AI-powered silent speech interpreter")
    print("designed to help people with speech impairments communicate naturally.")
    
    # Check dependencies
    if not check_dependencies():
        print("\n💡 Please install missing dependencies and try again.")
        return
    
    # Run selected mode
    success = False
    
    if args.mode == "complete":
        success = run_complete_demo()
    elif args.mode == "gui":
        success = run_gui()
    elif args.mode == "cli":
        success = run_cli_demo()
    elif args.mode == "train":
        success = run_training_only()
    
    # Final message
    print("\n" + "=" * 60)
    if success:
        print("🎉 Silexa demonstration completed successfully!")
        print("\n🚀 Next steps:")
        print("  • Collect real video data for better training")
        print("  • Train models with larger datasets")
        print("  • Deploy for real-world testing")
        print("  • Develop mobile applications")
    else:
        print("⚠️ Demonstration encountered issues.")
        print("Please check the error messages above.")
    
    print("\n💡 Silexa - Empowering Communication Through AI")
    print("   'Communication is a basic human right'")


if __name__ == "__main__":
    main()
