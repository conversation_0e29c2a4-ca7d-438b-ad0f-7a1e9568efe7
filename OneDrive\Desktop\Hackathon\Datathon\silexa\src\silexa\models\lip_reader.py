"""
Deep learning model for lip reading and text prediction
"""

import numpy as np
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from typing import Optional, Tuple, List, Dict, Any
import pickle
import os
from loguru import logger

from ..core.config import ModelConfig


class LipReadingModel:
    """
    Deep learning model for converting lip movement features to text.
    
    Supports multiple architectures:
    - LSTM-based sequence model
    - Transformer-based attention model
    - CNN-LSTM hybrid model
    """
    
    def __init__(self, config: ModelConfig):
        """
        Initialize the lip reading model.
        
        Args:
            config: Model configuration object
        """
        self.config = config
        self.model = None
        self.tokenizer = None
        self.vocab_size = 1000  # Default vocabulary size
        self.max_sequence_length = config.sequence_length
        
        # Model compilation settings
        self.optimizer = keras.optimizers.Adam(learning_rate=config.learning_rate)
        self.loss_function = 'sparse_categorical_crossentropy'
        self.metrics = ['accuracy']
        
        logger.info(f"LipReadingModel initialized with {config.model_type} architecture")
    
    def build_model(self, vocab_size: int = None) -> keras.Model:
        """
        Build the neural network model.
        
        Args:
            vocab_size: Size of the vocabulary
            
        Returns:
            Compiled Keras model
        """
        if vocab_size:
            self.vocab_size = vocab_size
        
        if self.config.model_type == "lstm":
            model = self._build_lstm_model()
        elif self.config.model_type == "transformer":
            model = self._build_transformer_model()
        elif self.config.model_type == "cnn_lstm":
            model = self._build_cnn_lstm_model()
        else:
            raise ValueError(f"Unsupported model type: {self.config.model_type}")
        
        # Compile model
        model.compile(
            optimizer=self.optimizer,
            loss=self.loss_function,
            metrics=self.metrics
        )
        
        self.model = model
        logger.info(f"Model built with {model.count_params()} parameters")
        return model
    
    def _build_lstm_model(self) -> keras.Model:
        """Build LSTM-based model."""
        inputs = keras.Input(shape=(self.config.sequence_length, self.config.feature_dim))
        
        # LSTM layers
        x = inputs
        for i in range(self.config.num_layers):
            return_sequences = i < self.config.num_layers - 1
            x = layers.LSTM(
                self.config.hidden_units,
                return_sequences=return_sequences,
                dropout=self.config.dropout_rate,
                recurrent_dropout=self.config.dropout_rate,
                name=f'lstm_{i+1}'
            )(x)
        
        # Dense layers
        x = layers.Dense(self.config.hidden_units, activation='relu', name='dense_1')(x)
        x = layers.Dropout(self.config.dropout_rate)(x)
        x = layers.Dense(self.config.hidden_units // 2, activation='relu', name='dense_2')(x)
        x = layers.Dropout(self.config.dropout_rate)(x)
        
        # Output layer
        outputs = layers.Dense(self.vocab_size, activation='softmax', name='output')(x)
        
        model = keras.Model(inputs, outputs, name='lip_reading_lstm')
        return model
    
    def _build_transformer_model(self) -> keras.Model:
        """Build Transformer-based model."""
        inputs = keras.Input(shape=(self.config.sequence_length, self.config.feature_dim))
        
        # Positional encoding
        x = self._add_positional_encoding(inputs)
        
        # Multi-head attention layers
        for i in range(self.config.num_layers):
            # Multi-head attention
            attention_output = layers.MultiHeadAttention(
                num_heads=8,
                key_dim=self.config.hidden_units // 8,
                dropout=self.config.dropout_rate,
                name=f'attention_{i+1}'
            )(x, x)
            
            # Add & Norm
            x = layers.Add()([x, attention_output])
            x = layers.LayerNormalization()(x)
            
            # Feed forward
            ff_output = layers.Dense(self.config.hidden_units, activation='relu')(x)
            ff_output = layers.Dropout(self.config.dropout_rate)(ff_output)
            ff_output = layers.Dense(self.config.feature_dim)(ff_output)
            
            # Add & Norm
            x = layers.Add()([x, ff_output])
            x = layers.LayerNormalization()(x)
        
        # Global average pooling
        x = layers.GlobalAveragePooling1D()(x)
        
        # Dense layers
        x = layers.Dense(self.config.hidden_units, activation='relu')(x)
        x = layers.Dropout(self.config.dropout_rate)(x)
        
        # Output layer
        outputs = layers.Dense(self.vocab_size, activation='softmax', name='output')(x)
        
        model = keras.Model(inputs, outputs, name='lip_reading_transformer')
        return model
    
    def _build_cnn_lstm_model(self) -> keras.Model:
        """Build CNN-LSTM hybrid model."""
        inputs = keras.Input(shape=(self.config.sequence_length, self.config.feature_dim))
        
        # Reshape for 1D convolution
        x = layers.Reshape((self.config.sequence_length, self.config.feature_dim, 1))(inputs)
        
        # 1D CNN layers
        x = layers.Conv2D(32, (3, 3), activation='relu', padding='same')(x)
        x = layers.MaxPooling2D((2, 1))(x)
        x = layers.Conv2D(64, (3, 3), activation='relu', padding='same')(x)
        x = layers.MaxPooling2D((2, 1))(x)
        
        # Reshape back for LSTM
        new_shape = x.shape
        x = layers.Reshape((new_shape[1], new_shape[2] * new_shape[3]))(x)
        
        # LSTM layers
        for i in range(self.config.num_layers):
            return_sequences = i < self.config.num_layers - 1
            x = layers.LSTM(
                self.config.hidden_units,
                return_sequences=return_sequences,
                dropout=self.config.dropout_rate,
                name=f'lstm_{i+1}'
            )(x)
        
        # Dense layers
        x = layers.Dense(self.config.hidden_units, activation='relu')(x)
        x = layers.Dropout(self.config.dropout_rate)(x)
        
        # Output layer
        outputs = layers.Dense(self.vocab_size, activation='softmax', name='output')(x)
        
        model = keras.Model(inputs, outputs, name='lip_reading_cnn_lstm')
        return model
    
    def _add_positional_encoding(self, inputs):
        """Add positional encoding for transformer model."""
        seq_len = self.config.sequence_length
        d_model = self.config.feature_dim
        
        # Create positional encoding
        position = np.arange(seq_len)[:, np.newaxis]
        div_term = np.exp(np.arange(0, d_model, 2) * -(np.log(10000.0) / d_model))
        
        pos_encoding = np.zeros((seq_len, d_model))
        pos_encoding[:, 0::2] = np.sin(position * div_term)
        pos_encoding[:, 1::2] = np.cos(position * div_term)
        
        pos_encoding = tf.constant(pos_encoding, dtype=tf.float32)
        pos_encoding = tf.expand_dims(pos_encoding, 0)
        
        return inputs + pos_encoding

    def train(self, X_train: np.ndarray, y_train: np.ndarray,
              X_val: np.ndarray = None, y_val: np.ndarray = None) -> keras.callbacks.History:
        """
        Train the model.

        Args:
            X_train: Training features
            y_train: Training labels
            X_val: Validation features (optional)
            y_val: Validation labels (optional)

        Returns:
            Training history
        """
        if self.model is None:
            raise ValueError("Model not built. Call build_model() first.")

        # Prepare callbacks
        callbacks = [
            keras.callbacks.EarlyStopping(
                patience=self.config.early_stopping_patience,
                restore_best_weights=True
            ),
            keras.callbacks.ReduceLROnPlateau(
                factor=0.5,
                patience=5,
                min_lr=1e-7
            )
        ]

        # Prepare validation data
        validation_data = None
        if X_val is not None and y_val is not None:
            validation_data = (X_val, y_val)

        # Train model
        history = self.model.fit(
            X_train, y_train,
            batch_size=self.config.batch_size,
            epochs=self.config.epochs,
            validation_data=validation_data,
            callbacks=callbacks,
            verbose=1
        )

        logger.info("Model training completed")
        return history

    def predict(self, features: np.ndarray) -> Tuple[str, float]:
        """
        Predict text from lip features.

        Args:
            features: Lip movement features

        Returns:
            Tuple of (predicted_text, confidence_score)
        """
        if self.model is None:
            raise ValueError("Model not loaded. Call load_model() or build_model() first.")

        try:
            # Ensure correct shape
            if len(features.shape) == 1:
                features = features.reshape(1, -1)

            # Predict
            predictions = self.model.predict(features, verbose=0)

            # Get most likely token
            predicted_token_id = np.argmax(predictions[0])
            confidence = float(np.max(predictions[0]))

            # Convert to text
            if self.tokenizer:
                predicted_text = self.tokenizer.decode([predicted_token_id])
            else:
                predicted_text = f"token_{predicted_token_id}"

            return predicted_text, confidence

        except Exception as e:
            logger.error(f"Error in prediction: {e}")
            return "", 0.0

    def predict_sequence(self, feature_sequence: np.ndarray) -> Tuple[str, float]:
        """
        Predict text from a sequence of lip features.

        Args:
            feature_sequence: Sequence of lip movement features

        Returns:
            Tuple of (predicted_text, confidence_score)
        """
        if self.model is None:
            raise ValueError("Model not loaded. Call load_model() or build_model() first.")

        try:
            # Ensure correct shape
            if len(feature_sequence.shape) == 2:
                feature_sequence = feature_sequence.reshape(1, *feature_sequence.shape)

            # Predict
            predictions = self.model.predict(feature_sequence, verbose=0)

            # Get most likely token
            predicted_token_id = np.argmax(predictions[0])
            confidence = float(np.max(predictions[0]))

            # Convert to text
            if self.tokenizer:
                predicted_text = self.tokenizer.decode([predicted_token_id])
            else:
                predicted_text = f"token_{predicted_token_id}"

            return predicted_text, confidence

        except Exception as e:
            logger.error(f"Error in sequence prediction: {e}")
            return "", 0.0

    def save_model(self, model_path: str) -> bool:
        """
        Save the trained model and tokenizer.

        Args:
            model_path: Path to save the model

        Returns:
            True if saved successfully, False otherwise
        """
        try:
            if self.model is None:
                logger.error("No model to save")
                return False

            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(model_path), exist_ok=True)

            # Save model
            self.model.save(model_path)

            # Save tokenizer if available
            if self.tokenizer:
                tokenizer_path = model_path.replace('.h5', '_tokenizer.pkl')
                with open(tokenizer_path, 'wb') as f:
                    pickle.dump(self.tokenizer, f)

            # Save config
            config_path = model_path.replace('.h5', '_config.pkl')
            with open(config_path, 'wb') as f:
                pickle.dump(self.config, f)

            logger.info(f"Model saved to {model_path}")
            return True

        except Exception as e:
            logger.error(f"Error saving model: {e}")
            return False

    def load_model(self, model_path: str) -> bool:
        """
        Load a trained model and tokenizer.

        Args:
            model_path: Path to the saved model

        Returns:
            True if loaded successfully, False otherwise
        """
        try:
            if not os.path.exists(model_path):
                logger.error(f"Model file not found: {model_path}")
                return False

            # Load model
            self.model = keras.models.load_model(model_path)

            # Load tokenizer if available
            tokenizer_path = model_path.replace('.h5', '_tokenizer.pkl')
            if os.path.exists(tokenizer_path):
                with open(tokenizer_path, 'rb') as f:
                    self.tokenizer = pickle.load(f)

            # Load config if available
            config_path = model_path.replace('.h5', '_config.pkl')
            if os.path.exists(config_path):
                with open(config_path, 'rb') as f:
                    self.config = pickle.load(f)

            logger.info(f"Model loaded from {model_path}")
            return True

        except Exception as e:
            logger.error(f"Error loading model: {e}")
            return False

    def evaluate(self, X_test: np.ndarray, y_test: np.ndarray) -> Dict[str, float]:
        """
        Evaluate the model on test data.

        Args:
            X_test: Test features
            y_test: Test labels

        Returns:
            Dictionary with evaluation metrics
        """
        if self.model is None:
            raise ValueError("Model not loaded. Call load_model() or build_model() first.")

        try:
            # Evaluate model
            results = self.model.evaluate(X_test, y_test, verbose=0)

            # Create results dictionary
            metrics_dict = {}
            for i, metric_name in enumerate(['loss'] + self.metrics):
                metrics_dict[metric_name] = float(results[i])

            logger.info(f"Model evaluation completed: {metrics_dict}")
            return metrics_dict

        except Exception as e:
            logger.error(f"Error in model evaluation: {e}")
            return {}

    def get_model_summary(self) -> str:
        """
        Get model architecture summary.

        Returns:
            Model summary as string
        """
        if self.model is None:
            return "No model loaded"

        import io
        import sys

        # Capture model summary
        old_stdout = sys.stdout
        sys.stdout = buffer = io.StringIO()

        self.model.summary()

        sys.stdout = old_stdout
        summary = buffer.getvalue()

        return summary
