"""
Working Silexa Demo with Real Text-to-Speech
"""

import sys
import os
import time
import random
import threading
import tkinter as tk
from tkinter import ttk, messagebox

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def speak_text_windows(text):
    """Use Windows built-in TTS"""
    try:
        # Method 1: Try Windows SAPI
        import win32com.client
        speaker = win32com.client.Dispatch("SAPI.SpVoice")
        speaker.Speak(text)
        return True, "Windows SAPI"
    except ImportError:
        pass
    except Exception as e:
        print(f"SAPI error: {e}")
    
    try:
        # Method 2: PowerShell TTS
        import subprocess
        escaped_text = text.replace('"', '""')
        cmd = f'powershell -Command "Add-Type -AssemblyName System.Speech; $synth = New-Object System.Speech.Synthesis.SpeechSynthesizer; $synth.Speak(\'{escaped_text}\')"'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            return True, "PowerShell TTS"
    except Exception as e:
        print(f"PowerShell TTS error: {e}")
    
    return False, "No TTS available"


class WorkingSilexaDemo:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Silexa - Working Demo with Real TTS")
        self.root.geometry("800x600")
        self.root.configure(bg='#667eea')
        
        # Vocabulary
        self.vocabulary = [
            "hello", "world", "yes", "no", "please", "thank", "you", "good", "bad",
            "help", "water", "food", "home", "work", "family", "friend", "love",
            "happy", "sad", "angry", "tired", "hungry", "thirsty", "cold", "hot"
        ]
        
        # Current state
        self.current_text = ""
        self.current_confidence = 0.0
        self.current_emotion = "neutral"
        self.is_demo_running = False
        self.demo_thread = None
        
        self.create_widgets()
        
    def create_widgets(self):
        """Create the GUI"""
        
        # Title
        title_frame = tk.Frame(self.root, bg='#667eea')
        title_frame.pack(fill=tk.X, pady=20)
        
        title_label = tk.Label(title_frame, text="🎯 SILEXA", 
                              font=("Arial", 24, "bold"), 
                              fg="white", bg='#667eea')
        title_label.pack()
        
        subtitle_label = tk.Label(title_frame, text="AI-Powered Silent Speech Interpreter", 
                                 font=("Arial", 12), 
                                 fg="white", bg='#667eea')
        subtitle_label.pack()
        
        # Main frame
        main_frame = tk.Frame(self.root, bg="white", relief=tk.RAISED, bd=2)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Prediction display
        pred_frame = tk.LabelFrame(main_frame, text="🎤 Live Recognition", 
                                  font=("Arial", 14, "bold"), fg="#667eea")
        pred_frame.pack(fill=tk.X, padx=20, pady=20)
        
        self.text_var = tk.StringVar(value="Click 'Start Demo' to begin")
        self.text_label = tk.Label(pred_frame, textvariable=self.text_var, 
                                  font=("Arial", 18, "bold"), 
                                  fg="#667eea", wraplength=600)
        self.text_label.pack(pady=10)
        
        # Confidence display
        conf_frame = tk.Frame(pred_frame)
        conf_frame.pack(fill=tk.X, padx=20, pady=10)
        
        tk.Label(conf_frame, text="Confidence:", font=("Arial", 10)).pack(side=tk.LEFT)
        
        self.confidence_var = tk.StringVar(value="0%")
        tk.Label(conf_frame, textvariable=self.confidence_var, 
                font=("Arial", 10, "bold")).pack(side=tk.RIGHT)
        
        # Progress bar for confidence
        self.confidence_bar = ttk.Progressbar(conf_frame, length=300, mode='determinate')
        self.confidence_bar.pack(side=tk.LEFT, padx=10, fill=tk.X, expand=True)
        
        # Emotion display
        emotion_frame = tk.Frame(pred_frame)
        emotion_frame.pack(fill=tk.X, padx=20, pady=5)
        
        tk.Label(emotion_frame, text="Emotion:", font=("Arial", 10)).pack(side=tk.LEFT)
        
        self.emotion_var = tk.StringVar(value="neutral")
        tk.Label(emotion_frame, textvariable=self.emotion_var, 
                font=("Arial", 10, "bold"), fg="#667eea").pack(side=tk.RIGHT)
        
        # Control buttons
        button_frame = tk.Frame(pred_frame)
        button_frame.pack(pady=15)
        
        self.start_button = tk.Button(button_frame, text="🚀 Start Demo", 
                                     command=self.toggle_demo,
                                     font=("Arial", 12, "bold"),
                                     bg="#667eea", fg="white",
                                     padx=20, pady=10)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.speak_button = tk.Button(button_frame, text="🔊 Speak Text", 
                                     command=self.speak_current,
                                     font=("Arial", 12, "bold"),
                                     bg="#28a745", fg="white",
                                     padx=20, pady=10)
        self.speak_button.pack(side=tk.LEFT, padx=5)
        
        self.clear_button = tk.Button(button_frame, text="🗑️ Clear", 
                                     command=self.clear_text,
                                     font=("Arial", 12, "bold"),
                                     bg="#dc3545", fg="white",
                                     padx=20, pady=10)
        self.clear_button.pack(side=tk.LEFT, padx=5)
        
        # Vocabulary section
        vocab_frame = tk.LabelFrame(main_frame, text="📚 Vocabulary (Click to Test)", 
                                   font=("Arial", 14, "bold"), fg="#667eea")
        vocab_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Create vocabulary grid
        vocab_container = tk.Frame(vocab_frame)
        vocab_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Add vocabulary words as buttons
        row = 0
        col = 0
        for word in self.vocabulary:
            btn = tk.Button(vocab_container, text=word,
                           command=lambda w=word: self.simulate_word(w),
                           font=("Arial", 10),
                           bg="#f8f9fa", fg="#333",
                           padx=10, pady=5,
                           relief=tk.RAISED, bd=1)
            btn.grid(row=row, column=col, padx=2, pady=2, sticky="ew")
            
            col += 1
            if col >= 6:  # 6 columns
                col = 0
                row += 1
        
        # Configure grid weights
        for i in range(6):
            vocab_container.columnconfigure(i, weight=1)
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready - TTS Available ✅")
        status_bar = tk.Label(self.root, textvariable=self.status_var, 
                             relief=tk.SUNKEN, anchor=tk.W,
                             bg="#f8f9fa", fg="#333")
        status_bar.pack(fill=tk.X, side=tk.BOTTOM)
    
    def toggle_demo(self):
        """Start/stop the demo"""
        if not self.is_demo_running:
            self.start_demo()
        else:
            self.stop_demo()
    
    def start_demo(self):
        """Start automatic demo"""
        self.is_demo_running = True
        self.start_button.config(text="⏹️ Stop Demo", bg="#dc3545")
        self.status_var.set("Demo running - Generating predictions...")
        
        # Start demo thread
        self.demo_thread = threading.Thread(target=self.demo_loop, daemon=True)
        self.demo_thread.start()
    
    def stop_demo(self):
        """Stop automatic demo"""
        self.is_demo_running = False
        self.start_button.config(text="🚀 Start Demo", bg="#667eea")
        self.status_var.set("Demo stopped - Ready")
    
    def demo_loop(self):
        """Automatic demo loop"""
        while self.is_demo_running:
            # Generate random prediction
            word = random.choice(self.vocabulary)
            confidence = random.uniform(0.75, 0.95)
            
            # Update UI in main thread
            self.root.after(0, self.update_prediction, word, confidence)
            
            # Wait before next prediction
            time.sleep(2.5)
    
    def simulate_word(self, word):
        """Simulate recognition of a specific word"""
        confidence = random.uniform(0.85, 0.98)
        self.update_prediction(word, confidence)
    
    def update_prediction(self, text, confidence):
        """Update the prediction display"""
        self.current_text = text
        self.current_confidence = confidence
        self.current_emotion = self.detect_emotion(text)
        
        # Update UI
        self.text_var.set(text)
        self.confidence_var.set(f"{confidence*100:.1f}%")
        self.confidence_bar['value'] = confidence * 100
        self.emotion_var.set(self.current_emotion)
        
        # Visual feedback
        self.text_label.config(fg="#28a745")
        self.root.after(1000, lambda: self.text_label.config(fg="#667eea"))
    
    def detect_emotion(self, text):
        """Simple emotion detection"""
        emotions = {
            'happy': ['good', 'great', 'excellent', 'wonderful', 'amazing', 'love', 'happy'],
            'sad': ['bad', 'terrible', 'awful', 'horrible', 'sad'],
            'excited': ['wow', 'incredible', 'awesome', 'brilliant'],
            'calm': ['okay', 'fine', 'normal', 'regular', 'water', 'home']
        }
        
        text_lower = text.lower()
        for emotion, keywords in emotions.items():
            if text_lower in keywords:
                return emotion
        return 'neutral'
    
    def speak_current(self):
        """Speak the current text using Windows TTS"""
        if not self.current_text or self.current_text == "Click 'Start Demo' to begin":
            messagebox.showwarning("No Text", "No text to speak! Start the demo or click a vocabulary word first.")
            return
        
        # Update button
        original_text = self.speak_button.config('text')[-1]
        self.speak_button.config(text="🔊 Speaking...", bg="#20c997")
        self.status_var.set(f"Speaking: '{self.current_text}'...")
        
        def speak_thread():
            try:
                success, method = speak_text_windows(self.current_text)
                
                # Update UI in main thread
                if success:
                    self.root.after(0, lambda: self.status_var.set(f"✅ Speech completed using {method}"))
                    self.root.after(0, lambda: messagebox.showinfo("Speech Success", 
                        f"🔊 Successfully spoke: '{self.current_text}'\n\n"
                        f"🎭 Emotion: {self.current_emotion}\n"
                        f"🎯 Confidence: {self.current_confidence*100:.1f}%\n"
                        f"🔧 Method: {method}\n\n"
                        f"✨ Silexa TTS with emotion awareness!"))
                else:
                    self.root.after(0, lambda: self.status_var.set("❌ Speech failed - TTS not available"))
                    self.root.after(0, lambda: messagebox.showerror("Speech Error", 
                        "Text-to-speech is not available on this system.\n\n"
                        "To enable TTS:\n"
                        "• Install pywin32: pip install pywin32\n"
                        "• Or ensure Windows Speech API is available"))
                
            except Exception as e:
                self.root.after(0, lambda: self.status_var.set(f"❌ Speech error: {e}"))
            finally:
                # Restore button
                self.root.after(0, lambda: self.speak_button.config(text=original_text, bg="#28a745"))
        
        # Run speech in separate thread to avoid blocking UI
        threading.Thread(target=speak_thread, daemon=True).start()
    
    def clear_text(self):
        """Clear the current text"""
        self.current_text = ""
        self.current_confidence = 0.0
        self.current_emotion = "neutral"
        
        self.text_var.set("Click 'Start Demo' to begin")
        self.confidence_var.set("0%")
        self.confidence_bar['value'] = 0
        self.emotion_var.set("neutral")
        
        self.status_var.set("Text cleared - Ready")
    
    def run(self):
        """Start the application"""
        self.root.mainloop()


def main():
    """Main function"""
    print("🎯 Starting Silexa Working Demo")
    print("✨ Features:")
    print("  • Real Windows text-to-speech")
    print("  • Interactive vocabulary")
    print("  • Live demo simulation")
    print("  • Emotion detection")
    print("\n🚀 Opening GUI...")
    
    try:
        app = WorkingSilexaDemo()
        app.run()
    except Exception as e:
        print(f"❌ Error starting demo: {e}")
        input("Press Enter to exit...")


if __name__ == "__main__":
    main()
