Collecting gTTS
  Downloading gTTS-2.5.4-py3-none-any.whl.metadata (4.1 kB)
Requirement already satisfied: requests<3,>=2.27 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from gTTS) (2.31.0)
Collecting click<8.2,>=7.1 (from gTTS)
  Using cached click-8.1.8-py3-none-any.whl.metadata (2.3 kB)
Requirement already satisfied: colorama in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from click<8.2,>=7.1->gTTS) (0.4.6)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from requests<3,>=2.27->gTTS) (3.4.2)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from requests<3,>=2.27->gTTS) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from requests<3,>=2.27->gTTS) (2.4.0)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from requests<3,>=2.27->gTTS) (2025.4.26)
Downloading gTTS-2.5.4-py3-none-any.whl (29 kB)
Using cached click-8.1.8-py3-none-any.whl (98 kB)
Installing collected packages: click, gTTS
  Attempting uninstall: click
    Found existing installation: click 8.2.1
    Uninstalling click-8.2.1:
      Successfully uninstalled click-8.2.1
Successfully installed click-8.1.8 gTTS-2.5.4
