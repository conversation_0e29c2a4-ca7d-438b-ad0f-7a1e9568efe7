"""
Training pipeline for lip reading models
"""

import numpy as np
import tensorflow as tf
from tensorflow import keras
import os
import json
from pathlib import Path
from typing import Dict, Tuple, Optional, List
from loguru import logger
import matplotlib.pyplot as plt

from ..core.config import SilexaConfig
from ..models.lip_reader import LipReadingModel
from .data_generator import LipReadingDataGenerator


class LipReadingTrainer:
    """
    Complete training pipeline for lip reading models.
    """
    
    def __init__(self, config: SilexaConfig):
        """Initialize the trainer"""
        self.config = config
        self.data_generator = LipReadingDataGenerator(config)
        self.model = None
        self.history = None
        
        # Set up directories
        self.models_dir = Path(config.data.models_dir)
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info("LipReadingTrainer initialized")
    
    def prepare_synthetic_dataset(self, num_samples: int = 2000) -> str:
        """
        Prepare a synthetic dataset for training.
        
        Args:
            num_samples: Number of samples to generate
            
        Returns:
            Path to the prepared dataset
        """
        logger.info("Preparing synthetic dataset...")
        
        # Generate synthetic data
        X, y = self.data_generator.generate_synthetic_data(num_samples)
        
        # Save dataset
        dataset_path = self.data_generator.save_dataset(X, y, "synthetic_basic")
        
        logger.info(f"Synthetic dataset prepared: {dataset_path}")
        return dataset_path
    
    def train_model(self, dataset_path: str, model_name: str = "lip_reader_basic") -> str:
        """
        Train a lip reading model.
        
        Args:
            dataset_path: Path to the training dataset
            model_name: Name for the saved model
            
        Returns:
            Path to the saved model
        """
        logger.info(f"Starting training with dataset: {dataset_path}")
        
        # Load dataset
        X, y, vocab_info = self.data_generator.load_dataset(dataset_path)
        vocab_size = vocab_info['vocab_size']
        
        # Create train/validation split
        X_train, X_val, y_train, y_val = self.data_generator.create_train_val_split(
            X, y, val_split=0.2
        )
        
        # Initialize model
        lip_reader = LipReadingModel(self.config.model)
        model = lip_reader.build_model(vocab_size=vocab_size)
        
        # Set up callbacks
        callbacks = [
            keras.callbacks.EarlyStopping(
                patience=self.config.model.early_stopping_patience,
                restore_best_weights=True,
                monitor='val_accuracy'
            ),
            keras.callbacks.ReduceLROnPlateau(
                factor=0.5,
                patience=5,
                min_lr=1e-7,
                monitor='val_loss'
            ),
            keras.callbacks.ModelCheckpoint(
                filepath=self.models_dir / f"{model_name}_best.h5",
                save_best_only=True,
                monitor='val_accuracy',
                mode='max'
            )
        ]
        
        # Train model
        logger.info("Starting model training...")
        history = model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            batch_size=self.config.model.batch_size,
            epochs=self.config.model.epochs,
            callbacks=callbacks,
            verbose=1
        )
        
        # Save final model
        model_path = self.models_dir / f"{model_name}.h5"
        model.save(model_path)
        
        # Save training history
        history_path = self.models_dir / f"{model_name}_history.json"
        with open(history_path, 'w') as f:
            # Convert numpy arrays to lists for JSON serialization
            history_dict = {key: [float(val) for val in values] 
                          for key, values in history.history.items()}
            json.dump(history_dict, f, indent=2)
        
        # Save model info
        model_info = {
            "model_name": model_name,
            "model_type": self.config.model.model_type,
            "vocab_size": vocab_size,
            "vocabulary": vocab_info['vocabulary'],
            "word_to_idx": vocab_info['word_to_idx'],
            "feature_dim": vocab_info['feature_dim'],
            "sequence_length": vocab_info['sequence_length'],
            "training_samples": len(X_train),
            "validation_samples": len(X_val),
            "final_accuracy": float(max(history.history.get('val_accuracy', [0]))),
            "final_loss": float(min(history.history.get('val_loss', [float('inf')])))
        }
        
        info_path = self.models_dir / f"{model_name}_info.json"
        with open(info_path, 'w') as f:
            json.dump(model_info, f, indent=2)
        
        self.model = model
        self.history = history
        
        logger.info(f"Training completed! Model saved to: {model_path}")
        logger.info(f"Final validation accuracy: {model_info['final_accuracy']:.4f}")
        
        return str(model_path)
    
    def evaluate_model(self, model_path: str, dataset_path: str) -> Dict[str, float]:
        """
        Evaluate a trained model.
        
        Args:
            model_path: Path to the trained model
            dataset_path: Path to the test dataset
            
        Returns:
            Dictionary with evaluation metrics
        """
        logger.info("Evaluating model...")
        
        # Load model
        model = keras.models.load_model(model_path)
        
        # Load dataset
        X, y, vocab_info = self.data_generator.load_dataset(dataset_path)
        
        # Evaluate
        results = model.evaluate(X, y, verbose=0)
        
        # Create results dictionary
        metrics = {}
        metric_names = ['loss'] + [m.name for m in model.metrics]
        
        for i, metric_name in enumerate(metric_names):
            metrics[metric_name] = float(results[i])
        
        # Additional metrics
        predictions = model.predict(X, verbose=0)
        predicted_classes = np.argmax(predictions, axis=1)
        
        # Accuracy per class
        unique_classes = np.unique(y)
        class_accuracies = {}
        
        for class_idx in unique_classes:
            class_mask = (y == class_idx)
            if np.sum(class_mask) > 0:
                class_acc = np.mean(predicted_classes[class_mask] == y[class_mask])
                word = vocab_info['vocabulary'][class_idx]
                class_accuracies[word] = float(class_acc)
        
        metrics['class_accuracies'] = class_accuracies
        metrics['overall_accuracy'] = float(np.mean(predicted_classes == y))
        
        logger.info(f"Evaluation completed - Overall accuracy: {metrics['overall_accuracy']:.4f}")
        
        return metrics
    
    def plot_training_history(self, model_name: str) -> str:
        """
        Plot training history.
        
        Args:
            model_name: Name of the model
            
        Returns:
            Path to the saved plot
        """
        if self.history is None:
            # Try to load history from file
            history_path = self.models_dir / f"{model_name}_history.json"
            if history_path.exists():
                with open(history_path, 'r') as f:
                    history_dict = json.load(f)
            else:
                logger.error("No training history available")
                return ""
        else:
            history_dict = self.history.history
        
        # Create plots
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
        
        # Plot accuracy
        if 'accuracy' in history_dict and 'val_accuracy' in history_dict:
            ax1.plot(history_dict['accuracy'], label='Training Accuracy')
            ax1.plot(history_dict['val_accuracy'], label='Validation Accuracy')
            ax1.set_title('Model Accuracy')
            ax1.set_xlabel('Epoch')
            ax1.set_ylabel('Accuracy')
            ax1.legend()
            ax1.grid(True)
        
        # Plot loss
        if 'loss' in history_dict and 'val_loss' in history_dict:
            ax2.plot(history_dict['loss'], label='Training Loss')
            ax2.plot(history_dict['val_loss'], label='Validation Loss')
            ax2.set_title('Model Loss')
            ax2.set_xlabel('Epoch')
            ax2.set_ylabel('Loss')
            ax2.legend()
            ax2.grid(True)
        
        plt.tight_layout()
        
        # Save plot
        plot_path = self.models_dir / f"{model_name}_training_plot.png"
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Training plot saved to: {plot_path}")
        return str(plot_path)
    
    def quick_train(self, num_samples: int = 1000, epochs: int = 20) -> str:
        """
        Quick training pipeline for demonstration.
        
        Args:
            num_samples: Number of synthetic samples
            epochs: Number of training epochs
            
        Returns:
            Path to the trained model
        """
        logger.info("Starting quick training pipeline...")
        
        # Temporarily adjust config for quick training
        original_epochs = self.config.model.epochs
        self.config.model.epochs = epochs
        
        try:
            # Prepare dataset
            dataset_path = self.prepare_synthetic_dataset(num_samples)
            
            # Train model
            model_path = self.train_model(dataset_path, "quick_demo_model")
            
            # Plot training history
            self.plot_training_history("quick_demo_model")
            
            # Evaluate model
            metrics = self.evaluate_model(model_path, dataset_path)
            
            logger.info("Quick training completed!")
            logger.info(f"Model accuracy: {metrics['overall_accuracy']:.4f}")
            
            return model_path
            
        finally:
            # Restore original config
            self.config.model.epochs = original_epochs
    
    def test_prediction(self, model_path: str, num_tests: int = 5) -> None:
        """
        Test model predictions with synthetic data.
        
        Args:
            model_path: Path to the trained model
            num_tests: Number of test predictions
        """
        logger.info("Testing model predictions...")
        
        # Load model
        model = keras.models.load_model(model_path)
        
        # Load model info
        info_path = model_path.replace('.h5', '_info.json')
        with open(info_path, 'r') as f:
            model_info = json.load(f)
        
        vocabulary = model_info['vocabulary']
        
        # Generate test data
        X_test, y_test = self.data_generator.generate_synthetic_data(num_tests)
        
        # Make predictions
        predictions = model.predict(X_test, verbose=0)
        predicted_classes = np.argmax(predictions, axis=1)
        
        # Show results
        logger.info("Test Predictions:")
        for i in range(num_tests):
            true_word = vocabulary[y_test[i]]
            pred_word = vocabulary[predicted_classes[i]]
            confidence = predictions[i][predicted_classes[i]]
            
            status = "✓" if y_test[i] == predicted_classes[i] else "✗"
            logger.info(f"  {status} True: '{true_word}' | Predicted: '{pred_word}' | Confidence: {confidence:.3f}")
    
    def get_available_models(self) -> List[str]:
        """Get list of available trained models"""
        model_files = list(self.models_dir.glob("*.h5"))
        return [f.stem for f in model_files]
